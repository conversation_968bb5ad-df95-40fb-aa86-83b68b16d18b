import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/utils/logger.dart';
import '../../../core/utils/timestamp_utils.dart';
import '../../../core/services/local_storage_service.dart';
import '../../../shared/providers/app_state_provider.dart';
import '../data/models/notification_models.dart';
import '../providers/notification_metadata_provider.dart';
import '../providers/notification_provider.dart';

/// Service to listen for new notifications from Firestore and sync to local storage
class NotificationListenerService {
  final Ref _ref;
  StreamSubscription<QuerySnapshot>? _notificationsSubscription;
  String? _currentUserId;

  NotificationListenerService(this._ref);

  /// Start listening for notifications when user is authenticated
  void startListening() {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      AppLogger.warning('NotificationListenerService: No authenticated user found');
      return;
    }

    if (_currentUserId == user.uid && _notificationsSubscription != null) {
      AppLogger.info('NotificationListenerService: Already listening for user ${user.uid}');
      return;
    }

    _currentUserId = user.uid;
    AppLogger.info('NotificationListenerService: Starting notification listener for user ${user.uid}');

    // Listen to new notifications (only unseen ones to reduce load)
    // New structure: users/{userId}/notifications/history/items/{notificationId}
    _notificationsSubscription = FirebaseFirestore.instance
        .collection('users')
        .doc(user.uid)
        .collection('notifications')
        .doc('history')
        .collection('items')
        .where('seen', isEqualTo: false)
        .orderBy('timestamp', descending: true)
        .limit(50) // Limit to recent notifications
        .snapshots()
        .listen(
          _handleNotificationsUpdate,
          onError: _handleError,
        );

    // The metadata listener will be started automatically by notificationMetadataListenerProvider
  }

  /// Stop listening for notifications
  void stopListening() {
    AppLogger.info('NotificationListenerService: Stopping notification listener');
    _notificationsSubscription?.cancel();
    _notificationsSubscription = null;
    _currentUserId = null;
    
    // The metadata listener will be stopped automatically by notificationMetadataListenerProvider
  }

  /// Handle notifications updates from Firestore
  void _handleNotificationsUpdate(QuerySnapshot snapshot) async {
    try {
      AppLogger.info('NotificationListenerService: Received ${snapshot.docs.length} notifications');

      if (snapshot.docs.isEmpty) {
        return;
      }

      // Process new notifications and sync to local storage
      final localStorageService = await _ref.read(localStorageServiceProvider.future);
      
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          
          // Convert Firestore timestamp to DateTime
          data['timestamp'] = TimestampUtils.convertTimestampToString(data['timestamp']);
          
          // Create notification object
          final notification = AppNotification.fromJson(data);
          
          // Save to local storage (organized by date)
          await _saveNotificationToLocalStorage(notification, localStorageService);
          
          AppLogger.info('NotificationListenerService: Synced notification ${notification.id} to local storage');
        } catch (e) {
          AppLogger.error('NotificationListenerService: Error processing notification ${doc.id}: $e');
        }
      }

      // Refresh the notification provider to show new notifications
      _ref.read(notificationNotifierProvider.notifier).refreshNotifications();
      
    } catch (e) {
      AppLogger.error('NotificationListenerService: Error handling notifications update: $e');
    }
  }

  /// Save notification to local storage organized by date
  Future<void> _saveNotificationToLocalStorage(
    AppNotification notification,
    LocalStorageService localStorageService,
  ) async {
    try {
      // Format date for storage key
      final dateKey = notification.timestamp.toIso8601String().split('T')[0]; // YYYY-MM-DD
      
      // Load existing notifications for this date
      final existingNotifications = localStorageService.loadNotificationsForDate(dateKey) ?? [];
      
      // Check if notification already exists
      final existingIndex = existingNotifications.indexWhere(
        (n) => (n as Map<String, dynamic>)['id'] == notification.id,
      );
      
      if (existingIndex >= 0) {
        // Update existing notification
        existingNotifications[existingIndex] = notification.toJson();
      } else {
        // Add new notification at the beginning (newest first)
        existingNotifications.insert(0, notification.toJson());
      }
      
      // Save updated notifications for this date
      await localStorageService.saveNotificationsForDate(dateKey, existingNotifications);
      
    } catch (e) {
      AppLogger.error('NotificationListenerService: Error saving notification to local storage: $e');
    }
  }

  /// Handle errors from the Firestore listener
  void _handleError(Object error) {
    AppLogger.error('NotificationListenerService: Firestore listener error: $error');
  }

  /// Restart listening (useful when user changes)
  void restartListening() {
    stopListening();
    startListening();
  }
}

/// Provider for the notification listener service
final notificationListenerServiceProvider = Provider<NotificationListenerService>((ref) {
  return NotificationListenerService(ref);
});

/// Provider to automatically start/stop notification listening based on auth state
final notificationListenerProvider = Provider<void>((ref) {
  final appState = ref.watch(appStateNotifierProvider);
  final listenerService = ref.read(notificationListenerServiceProvider);

  // Start listening when user is authenticated (don't wait for profile to be loaded)
  // The profile loading is just for UI purposes, but notifications should work immediately
  if (appState.authStatus == AuthStatus.authenticated && appState.user != null) {
    // User is authenticated, start listening
    AppLogger.info('NotificationListenerProvider: User authenticated, starting notification listener');
    listenerService.startListening();
  } else {
    // User is not authenticated, stop listening
    AppLogger.info('NotificationListenerProvider: User not authenticated, stopping listener');
    listenerService.stopListening();
  }

  // Clean up when provider is disposed
  ref.onDispose(() {
    listenerService.stopListening();
  });
});
