import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../meal_planning/data/models/meal_plan_request.dart';
import '../../data/services/meal_replacement_service.dart';
import '../../../meal_planning/providers/current_meal_plan_provider.dart';
import '../../providers/meal_replacement_provider.dart';

/// Bottom sheet widget for meal replacement functionality
class MealReplacementBottomSheet extends ConsumerStatefulWidget {
  final GeneratedMeal meal;
  final DayMealPlan dayMealPlan;

  const MealReplacementBottomSheet({
    super.key,
    required this.meal,
    required this.dayMealPlan,
  });

  @override
  ConsumerState<MealReplacementBottomSheet> createState() => _MealReplacementBottomSheetState();
}

class _MealReplacementBottomSheetState extends ConsumerState<MealReplacementBottomSheet> {
  bool _showGenerationView = false;
  bool _showCustomInstructions = false;
  final TextEditingController _customIngredientsController = TextEditingController();
  final FocusNode _customInstructionsFocusNode = FocusNode();
  MealReplacementService? _mealReplacementService;

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  /// Initialize the meal replacement service
  Future<void> _initializeService() async {
    try {
      _mealReplacementService = await ref.read(mealReplacementServiceProvider.future);
    } catch (e) {
      debugPrint('MealReplacementBottomSheet: Error initializing service: $e');
    }
  }

  @override
  void dispose() {
    _customIngredientsController.dispose();
    _customInstructionsFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'اختر طريقة إنشاء الوجبة',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Content
          Flexible(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: _showGenerationView
                  ? _buildGenerationOptionsView()
                  : _buildMainSelectionView(),
            ),
          ),
        ],
      ),
    );
  }

  /// Build main selection view (alternatives + generate button)
  Widget _buildMainSelectionView() {
    return SingleChildScrollView(
      key: const ValueKey('main_view'),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Always show alternatives section (includes current meal if no history)
          _buildExistingAlternativesSection(),
          const SizedBox(height: 24),

          // Generate new meal section
          _buildGenerateNewMealSection(),
          const SizedBox(height: 20), // Bottom padding
        ],
      ),
    );
  }



  /// Build existing alternatives section
  Widget _buildExistingAlternativesSection() {
    List<MealVersion> allVersions;
    MealVersion? selectedVersion;

    if (widget.meal.replacementHistory.isNotEmpty) {
      // Use existing versions from replacement history
      allVersions = widget.meal.getAllMealVersions();
      selectedVersion = widget.meal.getSelectedMealVersion();
    } else {
      // Create a version for the current meal when no replacement history exists
      final currentMealVersion = MealVersion(
        id: 'current',
        meal: widget.meal,
        title: 'الوجبة الحالية',
        isOriginal: true,
        createdAt: DateTime.now(),
      );
      allVersions = [currentMealVersion];
      selectedVersion = currentMealVersion; // Select the current meal by default
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.meal.replacementHistory.isNotEmpty ? 'البدائل المتاحة' : 'الوجبة الحالية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),

        // List of meal versions
        ...allVersions.map((version) {
          final isSelected = selectedVersion?.id == version.id;
          return _buildMealVersionTile(version, isSelected);
        }),
      ],
    );
  }

  /// Build meal version tile
  Widget _buildMealVersionTile(MealVersion version, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handleMealVersionSelection(version),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.primary.withValues(alpha: 0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? AppColors.primary
                    : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppColors.primary
                        : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.restaurant_menu,
                    color: isSelected ? Colors.white : Colors.grey.shade600,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        version.meal.name,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: isSelected
                              ? AppColors.primary
                              : AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${version.meal.nutrition.calories.toInt()} سعرة حرارية',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: AppColors.primary,
                    size: 24,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build generate new meal section
  Widget _buildGenerateNewMealSection() {
    final canGenerateMore = _mealReplacementService?.canGenerateMoreReplacements(widget.meal) ??
                           widget.meal.canGenerateMoreReplacements;
    final mealId = widget.meal.id;
    final hasError = mealId != null ? ref.watch(hasMealReplacementErrorProvider(mealId)) : false;
    final errorMessage = mealId != null ? ref.watch(getMealReplacementErrorProvider(mealId)) : null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أو إنشاء وجبة جديدة',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),

        // Show error message if there's an error
        if (hasError && errorMessage != null) ...[
          _buildErrorMessage(errorMessage),
          const SizedBox(height: 12),
        ],

        if (canGenerateMore) ...[
          _buildGenerateNewMealButton(),
        ] else ...[
          _buildReplacementLimitReached(),
        ],
      ],
    );
  }



  /// Build error message widget
  Widget _buildErrorMessage(String errorMessage) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'حدث خطأ في المحاولة الأخيرة لإنشاء الوجبة البديلة: $errorMessage',
              style: TextStyle(
                color: Colors.red.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build replacement limit reached message
  Widget _buildReplacementLimitReached() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.block,
            color: Colors.orange.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'تم الوصول للحد الأقصى من البدائل (3)',
              style: TextStyle(
                color: Colors.orange.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build generation options view (single-stage approach)
  Widget _buildGenerationOptionsView() {
    return SingleChildScrollView(
      key: const ValueKey('generation_view'),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back button
          Row(
            children: [
              IconButton(
                onPressed: () => setState(() => _showGenerationView = false),
                icon: const Icon(Icons.arrow_back),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
              const SizedBox(width: 8),
              Text(
                'خيارات إنشاء الوجبة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Generation options
          _buildGenerationOptions(),
          const SizedBox(height: 20), // Bottom padding
        ],
      ),
    );
  }

  /// Build generate new meal button
  Widget _buildGenerateNewMealButton() {
    final mealId = widget.meal.id;
    final isLoading = mealId != null ? ref.watch(shouldShowMealReplacementLoadingProvider(mealId)) : false;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isLoading ? null : () => setState(() => _showGenerationView = true),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isLoading
                  ? Colors.grey.shade100
                  : AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isLoading
                    ? Colors.grey.shade300
                    : AppColors.primary.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                if (isLoading) ...[
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.shade600),
                    ),
                  ),
                ] else ...[
                  Icon(
                    Icons.auto_awesome,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ],
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    isLoading ? 'جاري الإنشاء...' : 'إنشاء وجبة جديدة',
                    style: TextStyle(
                      color: isLoading ? Colors.grey.shade600 : AppColors.primary,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ),
                if (!isLoading) ...[
                  Icon(
                    Icons.arrow_forward_ios,
                    color: AppColors.primary,
                    size: 16,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build generation options (single-stage with expandable custom instructions)
  Widget _buildGenerationOptions() {
    final mealId = widget.meal.id;
    final isLoading = mealId != null ? ref.watch(shouldShowMealReplacementLoadingProvider(mealId)) : false;
    final hasCustomInstructions = _customIngredientsController.text.trim().isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Informational paragraph with embedded links
        _buildInformationalParagraph(),
        const SizedBox(height: 24),

        // Custom instructions text area (expandable)
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: _showCustomInstructions ? null : 0,
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 300),
            opacity: _showCustomInstructions ? 1.0 : 0.0,
            child: _showCustomInstructions ? _buildCustomInstructionsSection() : const SizedBox.shrink(),
          ),
        ),

        if (_showCustomInstructions) const SizedBox(height: 24),

        // Generate button
        _buildGenerateButton(isLoading, hasCustomInstructions),
      ],
    );
  }

  /// Build informational paragraph with embedded links
  Widget _buildInformationalParagraph() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.info_outline,
            color: AppColors.primary,
            size: 20,
          ),
          const SizedBox(height: 8),
          RichText(
            text: TextSpan(
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textPrimary,
                height: 1.5,
              ),
              children: [
                const TextSpan(
                  text: 'سيتم إنشاء وجبة بديلة باستخدام ',
                ),
                WidgetSpan(
                  child: GestureDetector(
                    onTap: _navigateToSettings,
                    child: Text(
                      'تفضيلاتك الغذائية',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
                const TextSpan(
                  text: ' المحفوظة مع مطابقة الخصائص الغذائية (السعرات والمغذيات الكبرى) للوجبة الأصلية.\n\n',
                ),
                const TextSpan(
                  text: 'يمكنك أيضاً ',
                ),
                WidgetSpan(
                  child: GestureDetector(
                    onTap: _toggleCustomInstructions,
                    child: Text(
                      'إضافة تعليمات مخصصة',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
                const TextSpan(
                  text: ' لتحديد مكونات متوفرة لديك أو أطعمة تريد تضمينها أو تجنبها.',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build custom instructions section
  Widget _buildCustomInstructionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تعليمات مخصصة',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _customIngredientsController,
          focusNode: _customInstructionsFocusNode,
          maxLines: 4,
          decoration: InputDecoration(
            hintText: 'مثال: لدي دجاج وأرز، أريد تجنب الطماطم، أفضل الأطعمة المشوية',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }

  /// Build generate button
  Widget _buildGenerateButton(bool isLoading, bool hasCustomInstructions) {
    final buttonText = isLoading
        ? 'جاري الإنشاء... ستتم إشعارك عند الانتهاء'
        : _showCustomInstructions && hasCustomInstructions
            ? 'إنشاء وجبة مع التعليمات المخصصة'
            : 'إنشاء وجبة بديلة';

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isLoading ? null : _generateMealReplacement,
        style: ElevatedButton.styleFrom(
          backgroundColor: isLoading ? Colors.grey : AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: isLoading
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Flexible(
                    child: Text(
                      buttonText,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              )
            : Text(
                buttonText,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  /// Navigate to plan settings page
  void _navigateToSettings() {
    Navigator.of(context).pop(); // Close bottom sheet first
    context.push('/profile/settings');
  }

  /// Toggle custom instructions visibility
  void _toggleCustomInstructions() {
    setState(() {
      _showCustomInstructions = !_showCustomInstructions;
    });

    // Auto-focus the text area when it becomes visible
    if (_showCustomInstructions) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _customInstructionsFocusNode.requestFocus();
      });
    }
  }



  /// Handle meal version selection
  Future<void> _handleMealVersionSelection(MealVersion version) async {
    try {
      // Close the bottom sheet first
      Navigator.of(context).pop();

      // Create a new meal with the selected version
      final updatedMeal = widget.meal.copyWith(
        selectedMealId: version.id,
        nutrition: version.meal.nutrition,
        name: version.meal.name,
        description: version.meal.description,
        ingredients: version.meal.ingredients,
        preparationTime: version.meal.preparationTime,
        difficulty: version.meal.difficulty,
        imageUrl: version.meal.imageUrl, // Include image URL from selected version
        imageGeneratedAt: version.meal.imageGeneratedAt, // Include image generation timestamp
      );

      // Update the meal in the current meal plan
      final currentMealPlanNotifier = ref.read(currentMealPlanNotifierProvider.notifier);
      final success = await currentMealPlanNotifier.selectMealVersionInPlan(
        mealDate: widget.dayMealPlan.date,
        mealType: widget.meal.type,
        selectedMeal: updatedMeal,
      );

      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديد البديل بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في تحديد البديل'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Generate new meal replacement with optional custom ingredients
  Future<void> _generateMealReplacement() async {
    final customIngredients = _customIngredientsController.text.trim();
    final customInstructionsToUse = customIngredients.isEmpty ? null : customIngredients;

    try {
      // Close the bottom sheet first
      Navigator.of(context).pop();

      // Check if day document ID is available
      if (widget.dayMealPlan.dayDocumentId == null) {
        throw Exception('معرف يوم الوجبة غير متوفر');
      }

      // Generate new meal using the meal replacement provider
      await ref.read(mealReplacementNotifierProvider.notifier).generateMealReplacement(
        currentMeal: widget.meal,
        dayDocumentId: widget.dayMealPlan.dayDocumentId!,
        mealDate: widget.dayMealPlan.date,
        customIngredients: customInstructionsToUse,
      );

      if (!mounted) return;

      final message = customInstructionsToUse != null
          ? 'تم بدء إنشاء وجبة مخصصة، ستتم إشعارك عند الانتهاء'
          : 'تم بدء إنشاء وجبة جديدة، ستتم إشعارك عند الانتهاء';

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء إنشاء الوجبة: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }
}
