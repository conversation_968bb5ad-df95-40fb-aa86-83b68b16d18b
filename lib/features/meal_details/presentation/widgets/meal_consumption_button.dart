import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../meal_planning/data/models/meal_plan_request.dart';
import '../../../meal_planning/providers/current_meal_plan_provider.dart';
import '../../data/services/meal_consumption_service.dart';

/// Widget for handling meal consumption toggle functionality
class MealConsumptionButton extends ConsumerWidget {
  final GeneratedMeal meal;
  final DateTime mealDate;
  final VoidCallback? onConsumptionToggled;
  final bool autoCloseOnSuccess;

  const MealConsumptionButton({
    super.key,
    required this.meal,
    required this.mealDate,
    this.onConsumptionToggled,
    this.autoCloseOnSuccess = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentMealPlanState = ref.watch(currentMealPlanNotifierProvider);
    final mealConsumptionService = ref.read(mealConsumptionServiceProvider);

    final isConsumed = meal.isConsumed;
    final canToggle = mealConsumptionService.canConsumeMeal(mealDate);
    final isLoading = currentMealPlanState.isLoading;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(20, 20, 20, 20 + MediaQuery.of(context).padding.bottom),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: (canToggle && !isLoading) ? () => _handleToggleConsumption(context, ref) : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: _getButtonColor(isConsumed, canToggle, mealConsumptionService),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          icon: isLoading
              ? SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Icon(
                  isConsumed ? Icons.undo : Icons.check_circle,
                  size: 24,
                ),
          label: Text(
            isLoading ? 'جاري التحديث...' : mealConsumptionService.getConsumptionButtonText(isConsumed, canToggle),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }

  /// Get button color based on consumption state and availability
  Color _getButtonColor(bool isConsumed, bool canToggle, MealConsumptionService service) {
    final colorName = service.getConsumptionButtonColor(isConsumed, canToggle);
    switch (colorName) {
      case 'orange':
        return Colors.orange.shade600;
      case 'green':
        return Colors.green.shade600;
      case 'grey':
      default:
        return Colors.grey.shade400;
    }
  }

  /// Handle consumption toggle with error handling and navigation
  Future<void> _handleToggleConsumption(BuildContext context, WidgetRef ref) async {
    try {
      // Validate consumption using service
      final mealConsumptionService = ref.read(mealConsumptionServiceProvider);
      final result = await mealConsumptionService.toggleMealConsumption(
        mealDate: mealDate,
        mealType: meal.type,
      );

      if (!result.success) {
        throw Exception(result.error ?? 'Failed to validate meal consumption');
      }

      // Use current meal plan provider to actually toggle consumption
      await ref.read(currentMealPlanNotifierProvider.notifier).toggleMealConsumption(
        mealDate: mealDate,
        mealType: meal.type,
      );

      // Call callback if provided
      onConsumptionToggled?.call();

      // Auto-close page after successful toggle if enabled
      if (autoCloseOnSuccess && context.mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث حالة الوجبة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
