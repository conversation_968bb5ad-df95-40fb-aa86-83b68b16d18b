/**
 * Test script to verify meal plan generation validation
 * This script tests the new validation logic that prevents meal plan generation
 * when there are existing days with dates >= current server time + 2 days
 */

const { validateMealPlanGeneration } = require('./functions/services/mealPlanGenerationService');
const { initializeFirebase } = require('./functions/config/firebase');
const { Timestamp } = require('firebase-admin/firestore');

async function testMealPlanValidation() {
  try {
    console.log('Testing meal plan generation validation...');
    
    // Initialize Firebase
    const admin = initializeFirebase();
    const db = admin.firestore();
    
    // Test user ID
    const testUserId = 'test_user_validation_' + Date.now();
    
    console.log('Test 1: No existing meals (should pass validation)');
    let result = await validateMealPlanGeneration(testUserId);
    console.log('Result:', result);
    console.assert(result.isValid === true, 'Should pass when no meals exist');
    
    console.log('\nTest 2: Existing meal 1 day in future (should pass validation)');
    // Add a meal 1 day in the future
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    await db.collection('users').doc(testUserId).collection('days').add({
      date: Timestamp.fromDate(tomorrow),
      meals: [{ name: 'Test Meal', type: 'breakfast' }],
      created_at: Timestamp.now()
    });
    
    result = await validateMealPlanGeneration(testUserId);
    console.log('Result:', result);
    console.assert(result.isValid === true, 'Should pass when meal is only 1 day in future');
    
    console.log('\nTest 3: Existing meal 2 days in future (should fail validation)');
    // Add a meal 2 days in the future
    const dayAfterTomorrow = new Date();
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);
    dayAfterTomorrow.setHours(0, 0, 0, 0);
    
    await db.collection('users').doc(testUserId).collection('days').add({
      date: Timestamp.fromDate(dayAfterTomorrow),
      meals: [{ name: 'Test Meal 2', type: 'lunch' }],
      created_at: Timestamp.now()
    });
    
    result = await validateMealPlanGeneration(testUserId);
    console.log('Result:', result);
    console.assert(result.isValid === false, 'Should fail when meal is 2 days in future');
    console.assert(result.errorMessage, 'Should have error message');
    console.assert(result.daysDifference >= 2, 'Should report correct days difference');
    
    console.log('\nTest 4: Existing meal 3 days in future (should fail validation)');
    // Add a meal 3 days in the future
    const threeDaysLater = new Date();
    threeDaysLater.setDate(threeDaysLater.getDate() + 3);
    threeDaysLater.setHours(0, 0, 0, 0);
    
    await db.collection('users').doc(testUserId).collection('days').add({
      date: Timestamp.fromDate(threeDaysLater),
      meals: [{ name: 'Test Meal 3', type: 'dinner' }],
      created_at: Timestamp.now()
    });
    
    result = await validateMealPlanGeneration(testUserId);
    console.log('Result:', result);
    console.assert(result.isValid === false, 'Should fail when meal is 3 days in future');
    console.assert(result.daysDifference >= 2, 'Should report correct days difference');
    
    // Cleanup test data
    console.log('\nCleaning up test data...');
    const daysSnapshot = await db.collection('users').doc(testUserId).collection('days').get();
    const batch = db.batch();
    daysSnapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });
    await batch.commit();
    
    // Delete test user document if it exists
    const userDoc = await db.collection('users').doc(testUserId).get();
    if (userDoc.exists) {
      await userDoc.ref.delete();
    }
    
    console.log('✅ All tests passed! Validation logic is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testMealPlanValidation()
    .then(() => {
      console.log('Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testMealPlanValidation };
