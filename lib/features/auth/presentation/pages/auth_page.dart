import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../../core/router/app_routes.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/services/firestore_service.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../../../generated/l10n/app_localizations.dart';

import '../../../../shared/providers/app_state_provider.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../providers/auth_provider.dart';

class AuthPage extends ConsumerStatefulWidget {
  const AuthPage({super.key});

  @override
  ConsumerState<AuthPage> createState() => _AuthPageState();
}

class _AuthPageState extends ConsumerState<AuthPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Listen to auth state changes for automatic navigation
    ref.listen<AppState>(appStateNotifierProvider, (previous, next) {
      if (mounted && !_isLoading) {
        if (next.authStatus == AuthStatus.authenticated && previous?.authStatus != AuthStatus.authenticated) {
          AppLogger.info('Auth state changed to authenticated, navigating based on profile completion');
          _navigateBasedOnProfileCompletion();
        }
      }
    });

    return _buildUI(context);
  }

  Future<void> _handleGoogleLogin() async {
    setState(() => _isLoading = true);

    try {
      // Pass current path to track where authentication was initiated
      final currentPath = GoRouterState.of(context).uri.path;
      final authProvider = ref.read(authProviderProvider.notifier);
      final success = await authProvider.signInWithGoogle(currentPath);

      if (mounted) {
        if (success) {
          AppLogger.info('Google Sign-In successful, navigating based on profile completion');
          // Navigate based on profile completion status
          await _navigateBasedOnProfileCompletion();
        } else {
          // Show error message from auth provider
          final authState = ref.read(authProviderProvider);
          if (authState.generalError != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(authState.generalError!),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        AppLogger.error('Google Sign-In error: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Google sign-in failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Navigate user based on their profile completion status
  Future<void> _navigateBasedOnProfileCompletion() async {
    try {
      setState(() => _isLoading = true);

      // Add a small delay to allow provider dependencies to stabilize
      await Future.delayed(const Duration(milliseconds: 100));

      final appState = ref.read(appStateNotifierProvider);
      final user = appState.user;

      if (user == null) {
        // No authenticated user, navigate to onboarding
        if (mounted) {
          context.go(AppRoutes.onboarding);
        }
        return;
      }

      // Enhanced login flow: Query Firestore for user preferences
      await _loadUserPreferencesFromFirestore(user.uid);

      // Check onboarding completion status from Firestore
      final onboardingCompleted = await _checkOnboardingCompletion(user.uid);

      if (onboardingCompleted) {
        // Execute complete data loading sequence
        await _executeCompleteDataLoading(user.uid);
        // Navigate to home with replacement (no back navigation to auth)
        if (mounted) {
          context.go(AppRoutes.home);
        }
      } else {
        // Check for saved onboarding step
        await _navigateToOnboardingStep();
      }
    } catch (e) {
      // On error, default to onboarding
      if (mounted) {
        context.go(AppRoutes.onboarding);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Load user preferences from Firestore and save to local storage
  Future<void> _loadUserPreferencesFromFirestore(String userId) async {
    try {
      final firestoreService = ref.read(firestoreServiceProvider);
      final storageService = await ref.read(localStorageServiceProvider.future);

      // Query Firestore for user preferences
      final preferencesDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .collection('preferences')
          .doc('plan_preferences')
          .get();

      final notificationSettingsDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .collection('preferences')
          .doc('notification_settings')
          .get();

      // Save preferences to FlutterSecureStorage
      if (preferencesDoc.exists && preferencesDoc.data() != null) {
        await storageService.storeSecureObject('plan_preferences', preferencesDoc.data()!);
      }

      if (notificationSettingsDoc.exists && notificationSettingsDoc.data() != null) {
        await storageService.storeSecureObject('notification_settings', notificationSettingsDoc.data()!);
      }
    } catch (e) {
      AppLogger.warning('Failed to load user preferences from Firestore: $e');
      // Continue with login flow even if preferences loading fails
    }
  }

  /// Check onboarding completion status from Firestore
  Future<bool> _checkOnboardingCompletion(String userId) async {
    try {
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      if (userDoc.exists && userDoc.data() != null) {
        final data = userDoc.data()!;
        return data['onboarding_completed'] as bool? ?? false;
      }
      return false;
    } catch (e) {
      AppLogger.warning('Failed to check onboarding completion: $e');
      return false;
    }
  }

  /// Execute complete data loading sequence
  Future<void> _executeCompleteDataLoading(String userId) async {
    try {
      final storageService = await ref.read(localStorageServiceProvider.future);

      // Load meal plans from users/{userId}/days collection
      final daysQuery = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .collection('days')
          .orderBy('date', descending: false)
          .get();

      if (daysQuery.docs.isNotEmpty) {
        // Convert Firestore documents to meal plan data
        final mealPlanData = daysQuery.docs.map((doc) {
          final data = doc.data();
          data['id'] = doc.id;
          return data;
        }).toList();

        // Store meal data in meal_plans FlutterSecureStorage key
        await storageService.storeSecureObject('meal_plans', {
          'days': mealPlanData,
          'loaded_at': DateTime.now().toIso8601String(),
        });
      }

      // Verify plan_preferences and notification_settings are in local storage
      final planPrefs = await storageService.getSecureObject('plan_preferences');
      final notificationSettings = await storageService.getSecureObject('notification_settings');

      if (planPrefs == null || notificationSettings == null) {
        throw Exception('Required preferences not found in local storage');
      }

      AppLogger.info('Complete data loading sequence completed successfully');
    } catch (e) {
      AppLogger.error('Failed to execute complete data loading: $e');
      throw e;
    }
  }

  /// Navigate to appropriate onboarding step
  Future<void> _navigateToOnboardingStep() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final savedStep = localStorageService.loadCurrentOnboardingStep();

      if (savedStep != null) {
        // Navigate to specific onboarding step and clear saved step
        await localStorageService.clearCurrentOnboardingStep();
        if (mounted) {
          context.go('${AppRoutes.onboarding}?step=$savedStep');
        }
      } else {
        // Navigate to onboarding start (step 1)
        if (mounted) {
          context.go(AppRoutes.onboarding);
        }
      }
    } catch (e) {
      AppLogger.warning('Failed to navigate to onboarding step: $e');
      if (mounted) {
        context.go(AppRoutes.onboarding);
      }
    }
  }



  Future<void> _handleSkip() async {
    setState(() => _isLoading = true);

    try {
      AppLogger.info('Skip button clicked - setting guest user state');

      // Mark user as guest and navigate to onboarding
      await ref.read(appStateNotifierProvider.notifier).setGuestUser(true);

      AppLogger.info('Guest user state set, navigating to onboarding');

      if (mounted) {
        context.go(AppRoutes.onboarding);
        AppLogger.info('Navigation to onboarding initiated');
      }
    } catch (e) {
      AppLogger.error('Error in skip handler: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Widget _buildUI(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Spacer(),

              // Logo and Welcome Text
              FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.restaurant_menu,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 32),
                    Text(
                      l10n.appName,
                      style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'مرحباً بك في تطبيق التغذية الذكي',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اختر طريقة الدخول للمتابعة',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // Authentication Options
              SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Google Login Button
                      CustomButton(
                        text: 'تسجيل الدخول بجوجل',
                        onPressed: _isLoading ? null : _handleGoogleLogin,
                        icon: Icon(Icons.g_mobiledata, color: AppColors.textPrimary),
                        backgroundColor: Colors.white,
                        textColor: AppColors.textPrimary,
                        isOutlined: true,
                        isLoading: _isLoading,
                      ),

                      const SizedBox(height: 24),

                      // Divider
                      Row(
                        children: [
                          const Expanded(child: Divider()),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Text(
                              'أو',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ),
                          const Expanded(child: Divider()),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // Skip Button
                      TextButton(
                        onPressed: _isLoading ? null : _handleSkip,
                        child: Text(
                          'تخطي وإنشاء ملف شخصي',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Terms and Privacy
              FadeTransition(
                opacity: _fadeAnimation,
                child: Text(
                  'بالمتابعة، أنت توافق على شروط الاستخدام وسياسة الخصوصية',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
