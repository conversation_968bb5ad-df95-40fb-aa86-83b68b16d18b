
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/local_storage_service.dart';
import '../../../meal_planning/data/models/meal_plan_request.dart';

/// Provider for meal details service
final mealDetailsServiceProvider = FutureProvider<MealDetailsService>((ref) async {
  final localStorageService = await ref.watch(localStorageServiceProvider.future);

  return FirebaseMealDetailsService(localStorageService);
});

/// Result class for meal details operations
class MealDetailsOperationResult {
  const MealDetailsOperationResult({
    required this.success,
    this.data,
    this.error,
  });

  final bool success;
  final dynamic data;
  final String? error;
}

/// Abstract meal details service interface
abstract class MealDetailsService {
  // Utility methods
  String generateMealId(GeneratedMeal meal, DayMealPlan dayMealPlan);
  String formatMealDate(DateTime date);
}

/// Firebase implementation of meal details service
class FirebaseMealDetailsService implements MealDetailsService {
  final LocalStorageService _localStorageService;

  FirebaseMealDetailsService(this._localStorageService);





  @override
  String generateMealId(GeneratedMeal meal, DayMealPlan dayMealPlan) {
    return meal.id ?? '${meal.type}_${dayMealPlan.date.millisecondsSinceEpoch}';
  }

  @override
  String formatMealDate(DateTime date) {
    return date.toIso8601String().split('T')[0];
  }
}
