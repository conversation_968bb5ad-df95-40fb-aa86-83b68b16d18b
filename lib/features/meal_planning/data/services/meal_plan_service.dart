import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:easydietai/core/services/firebase_functions_service.dart';
import 'package:easydietai/core/services/firestore_service.dart';
import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/core/utils/logger.dart';
import '../models/meal_plan_request.dart';

part 'meal_plan_service.g.dart';

class MealPlanService {
  final FirebaseFunctionsService _functionsService;
  final FirestoreService _firestoreService;
  final LocalStorageService _localStorageService;

  MealPlanService(
    this._functionsService,
    this._firestoreService,
    this._localStorageService,
  );

  /// Generate a meal plan using Firebase Functions
  /// Preferences are automatically retrieved from user profile in Firestore
  /// Returns immediate response indicating if generation started successfully
  /// Actual meal plan data will be available via Firestore real-time updates
  Future<dynamic> generateMealPlan() async {
    return callGenerateMealPlanFunction();
  }

  /// Generate image for a meal using Firebase Functions
  /// Returns immediate response indicating if image generation started successfully
  /// Actual image URL will be available via Firestore real-time updates
  Future<dynamic> generateMealImage({
    String? dayDocumentId,
    String? mealDate,
    required String mealId,
  }) async {
    try {
      // Prepare request data - either dayDocumentId or mealDate is required
      final Map<String, dynamic> requestData = {
        'meal_id': mealId,
      };

      if (dayDocumentId != null && dayDocumentId.isNotEmpty) {
        requestData['day_document_id'] = dayDocumentId;
      } else if (mealDate != null && mealDate.isNotEmpty) {
        requestData['meal_date'] = mealDate;
      } else {
        throw Exception('Either dayDocumentId or mealDate must be provided');
      }

      final response = await _functionsService.post(
        endpoint: '/meal-plans/generate-image',
        data: requestData,
        logContext: 'Generating meal image',
      );

      return AsyncMealPlanResponse.fromJson(response);
    } catch (e) {
      throw Exception('Failed to generate meal image: $e');
    }
  }

  /// Call Firebase Functions to generate meal plan
  Future<dynamic> callGenerateMealPlanFunction() async {
    try {
      final response = await _functionsService.post(
        endpoint: '/meal-plans/generate',
        data: {},
        logContext: 'Generating meal plan',
      );

      // Check if response contains mealPlan data (synchronous response)
      if (response.containsKey('mealPlan') && response['mealPlan'] != null) {
        return MealPlanResponse.fromJson(response);
      } else {
        // Async response - no meal plan data, just success status
        return AsyncMealPlanResponse.fromJson(response);
      }
    } catch (e) {
      throw Exception('Failed to generate meal plan: $e');
    }
  }

  /// Get user's meal plans
  Future<List<MealPlanResponse>> getUserMealPlans({
    required String userId,
    int limit = 10,
    String status = 'active',
  }) async {
    try {
      final response = await getMealPlans(
        limit: limit,
        status: status,
      );

      final mealPlansData = response['mealPlans'] as List<dynamic>? ?? [];

      return mealPlansData
          .map((planData) => MealPlanResponse.fromJson(planData as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch meal plans: $e');
    }
  }

  /// Analyze nutrition for food items
  Future<NutritionInfo> analyzeNutrition({
    required List<String> foodItems,
    required String portion,
  }) async {
    return callAnalyzeNutritionFunction(
      foodItems: foodItems,
      portion: portion,
    );
  }

  /// Call Firebase Functions to analyze nutrition
  Future<NutritionInfo> callAnalyzeNutritionFunction({
    required List<String> foodItems,
    String portion = '1 serving',
  }) async {
    try {
      final response = await _functionsService.post(
        endpoint: '/analyze-nutrition',
        data: {
          'foodItems': foodItems,
          'portion': portion,
        },
        logContext: 'Analyzing nutrition for: $foodItems',
      );

      final nutritionData = response['nutrition'] as Map<String, dynamic>;
      return NutritionInfo.fromJson(nutritionData);
    } catch (e) {
      throw Exception('Failed to analyze nutrition: $e');
    }
  }

  /// Replace a meal with a new suggestion using Firebase Functions
  /// Returns immediate response indicating if replacement generation started successfully
  /// Actual replacement data will be available via Firestore real-time updates
  Future<Map<String, dynamic>> replaceMeal({
    required String dayDocumentId,
    required String mealId,
    String? customIngredients,
  }) async {
    return callReplaceMealFunction(
      dayDocumentId: dayDocumentId,
      mealId: mealId,
      customIngredients: customIngredients,
    );
  }

  /// Call Firebase Functions to replace meal
  Future<Map<String, dynamic>> callReplaceMealFunction({
    required String dayDocumentId,
    required String mealId,
    String? customIngredients,
  }) async {
    try {
      final response = await _functionsService.post(
        endpoint: '/meal-plans/replace-meal',
        data: {
          'day_document_id': dayDocumentId,  // Use snake_case as expected by Firebase function
          'meal_id': mealId,                 // Use snake_case as expected by Firebase function
          if (customIngredients != null) 'custom_ingredients': customIngredients,  // Use snake_case
        },
        logContext: 'Requesting meal replacement for meal ID $mealId in day document $dayDocumentId',
      );

      return response;
    } catch (e) {
      throw Exception('Failed to request meal replacement: $e');
    }
  }

  /// Get meal plans using Firebase Functions
  Future<Map<String, dynamic>> getMealPlans({
    int limit = 10,
    String status = 'active',
  }) async {
    try {
      final userId = _functionsService.getCurrentUserId();
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final response = await _functionsService.get(
        endpoint: '/meal-plans/$userId',
        queryParameters: {
          'limit': limit.toString(),
          'status': status,
        },
        logContext: 'Fetching meal plans for user: $userId',
      );

      return response;
    } catch (e) {
      throw Exception('Failed to fetch meal plans: $e');
    }
  }

  /// Generate custom meal plan with specific preferences
  Future<Map<String, dynamic>> generateCustomMealPlan({
    required Map<String, dynamic> preferences,
    int duration = 7,
  }) async {
    try {
      final response = await _functionsService.post(
        endpoint: '/meal-plans/generate-custom',
        data: {
          'preferences': preferences,
          'duration': duration,
        },
        logContext: 'Generating custom meal plan',
      );

      return response;
    } catch (e) {
      throw Exception('Failed to generate custom meal plan: $e');
    }
  }

  /// Get meal suggestions based on dietary preferences
  Future<Map<String, dynamic>> getMealSuggestions({
    Map<String, dynamic>? dietaryPreferences,
    String? mealType,
    int? maxSuggestions,
  }) async {
    try {
      final response = await _functionsService.get(
        endpoint: '/meal-plans/suggestions',
        queryParameters: {
          if (dietaryPreferences != null) 'preferences': dietaryPreferences.toString(),
          if (mealType != null) 'meal_type': mealType,
          if (maxSuggestions != null) 'max_suggestions': maxSuggestions.toString(),
        },
        logContext: 'Getting meal suggestions',
      );

      return response;
    } catch (e) {
      throw Exception('Failed to get meal suggestions: $e');
    }
  }

  /// Analyze meal compatibility with dietary restrictions
  Future<Map<String, dynamic>> analyzeMealCompatibility({
    required Map<String, dynamic> meal,
    required List<String> dietaryRestrictions,
  }) async {
    try {
      final response = await _functionsService.post(
        endpoint: '/meal-plans/analyze-compatibility',
        data: {
          'meal': meal,
          'dietary_restrictions': dietaryRestrictions,
        },
        logContext: 'Analyzing meal compatibility',
      );

      return response;
    } catch (e) {
      throw Exception('Failed to analyze meal compatibility: $e');
    }
  }

  // Meal Plan Sync Operations

  /// Check if user is authenticated
  bool get _isAuthenticated => FirebaseAuth.instance.currentUser != null;

  /// Get current user ID
  String? get _currentUserId => FirebaseAuth.instance.currentUser?.uid;

  /// Sync current meal plan from local storage to Firestore using intelligent incremental sync
  /// Only creates/updates new or modified day documents, preserves existing unchanged documents
  Future<bool> syncCurrentMealPlanToFirestore() async {
    if (!_isAuthenticated) {
      AppLogger.info('MealPlanService: User not authenticated, skipping meal plan sync');
      return false;
    }

    try {
      // Get meal plan data from local storage
      final mealPlanData = _localStorageService.loadCurrentMealPlan();
      if (mealPlanData == null) {
        AppLogger.info('MealPlanService: No meal plan data found in local storage');
        return false;
      }

      // Parse the meal plan data
      final generatedMealPlan = GeneratedMealPlan.fromJson(mealPlanData);

      // Get existing meal plan metadata from Firestore
      final existingMetadata = await _getExistingMealPlanMetadata();

      // Determine sync strategy based on existing data
      final syncResult = await _performIncrementalSyncWithModifications(generatedMealPlan, existingMetadata);

      if (syncResult) {
        AppLogger.info('MealPlanService: Successfully synced meal plan with incremental strategy');
        return true;
      } else {
        AppLogger.info('MealPlanService: Incremental sync failed, falling back to full sync');
        return await _performFullSync(generatedMealPlan);
      }
    } catch (e) {
      AppLogger.error('MealPlanService: Error syncing meal plan: $e');
      return false;
    }
  }

  /// Sync meal plan data from Firestore to local storage
  /// Reconstructs meal plan from individual day documents
  Future<bool> syncMealPlanFromFirestore() async {
    if (!_isAuthenticated) {
      AppLogger.info('MealPlanService: User not authenticated, skipping meal plan sync from Firestore');
      return false;
    }

    try {
      AppLogger.info('MealPlanService: Fetching meal plan data from Firestore for user: $_currentUserId');

      // Get user metadata to check if meal plan exists
      // final userDoc = await _firestoreService.getDocument('users/$_currentUserId');
      //
      // if (userDoc == null) {
      //   AppLogger.info('MealPlanService: User document not found in Firestore');
      //   return false;
      // }

      // Fetch all day documents directly from the days collection
      final daysSnapshot = await _firestoreService.getUserSubcollectionDocuments(
        subcollectionName: 'days',
        orderByField: 'date',
        descending: false,
      );

      if (daysSnapshot.isEmpty) {
        AppLogger.info('MealPlanService: No day documents found in Firestore');
        return false;
      }

      // Filter day documents to only include those within sync window
      final dateRange = _calculateSyncDateRange();
      final filteredDayDocuments = daysSnapshot.where((dayData) {
        final date = (dayData['date'] as Timestamp).toDate();
        return date.isAfter(dateRange['startDate']!) || _isSameDay(date, dateRange['startDate']!);
      }).toList();

      AppLogger.info('MealPlanService: Found ${filteredDayDocuments.length} day documents within sync window (filtered from ${daysSnapshot.length} total days)');

      // Reconstruct the meal plan from filtered day documents
      final reconstructedMealPlan = _reconstructMealPlanFromDayDocuments(filteredDayDocuments);

      // Save to local storage
      final success = await _localStorageService.saveCurrentMealPlan(reconstructedMealPlan.toJson());

      if (success) {
        final dateRange = _calculateSyncDateRange();
        AppLogger.info('MealPlanService: Successfully synced meal plan from Firestore to local storage (${filteredDayDocuments.length} days within sync window, filtered from ${daysSnapshot.length} total days, sync window: ${dateRange['startDate']} to unlimited future)');
        return true;
      } else {
        AppLogger.error('MealPlanService: Failed to save meal plan to local storage');
        return false;
      }
    } catch (e) {
      AppLogger.error('MealPlanService: Error syncing meal plan from Firestore: $e');
      return false;
    }
  }

  // Helper methods for sync operations

  /// Get existing meal plan metadata from Firestore
  Future<Map<String, dynamic>?> _getExistingMealPlanMetadata() async {
    try {
      final userDoc = await _firestoreService.getDocument('users/$_currentUserId');

      if (userDoc != null) {
        return userDoc['mealPlan'] as Map<String, dynamic>?;
      }
      return null;
    } catch (e) {
      AppLogger.error('MealPlanService: Error getting existing meal plan metadata: $e');
      return null;
    }
  }

  /// Calculate sync date range (3 past days + today + unlimited future)
  Map<String, DateTime> _calculateSyncDateRange() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final startDate = today.subtract(const Duration(days: 3));

    return {
      'startDate': startDate,
      'today': today,
    };
  }

  /// Check if two dates are the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// Reconstruct a GeneratedMealPlan from individual day documents
  GeneratedMealPlan _reconstructMealPlanFromDayDocuments(List<Map<String, dynamic>> dayDocuments) {
    final days = <DayMealPlan>[];

    // Sort day documents by date
    dayDocuments.sort((a, b) {
      final dateA = (a['date'] as Timestamp).toDate();
      final dateB = (b['date'] as Timestamp).toDate();
      return dateA.compareTo(dateB);
    });

    for (final dayData in dayDocuments) {
      try {
        final date = (dayData['date'] as Timestamp).toDate();
        final mealsData = dayData['meals'] as List<dynamic>;
        final nutritionData = dayData['total_nutrition'] as Map<String, dynamic>;

        // Convert meals data
        final meals = mealsData.map((mealData) {
          return GeneratedMeal.fromJson(mealData as Map<String, dynamic>);
        }).toList();

        // Convert nutrition data
        final totalNutrition = NutritionInfo.fromJson(nutritionData);

        // Get day document ID from the document
        final dayDocumentId = dayData['day_document_id'] as String?;
        AppLogger.info('MealPlanService: Debug - day_document_id value: $dayDocumentId (type: ${dayDocumentId.runtimeType})');

        final dayMealPlan = DayMealPlan(
          date: date,
          meals: meals,
          totalNutrition: totalNutrition,
          dayDocumentId: dayDocumentId,
        );

        AppLogger.info('MealPlanService: Created DayMealPlan for ${date.toIso8601String()} with dayDocumentId: ${dayMealPlan.dayDocumentId}');

        // Debug: Check JSON serialization
        final dayJson = dayMealPlan.toJson();
        AppLogger.info('MealPlanService: Debug - DayMealPlan JSON contains day_document_id: ${dayJson['day_document_id']}');

        days.add(dayMealPlan);
      } catch (e) {
        AppLogger.error('MealPlanService: Error reconstructing day from document: $e');
        // Skip this day if there's an error
      }
    }

    return GeneratedMealPlan(days: days);
  }

  /// Perform incremental sync with modifications
  Future<bool> _performIncrementalSyncWithModifications(
    GeneratedMealPlan generatedMealPlan,
    Map<String, dynamic>? existingMetadata,
  ) async {
    try {
      // Filter days to only include those within sync window
      final filteredDays = _filterDaysForSync(generatedMealPlan.days);

      if (filteredDays.isEmpty) {
        AppLogger.info('MealPlanService: No days within sync window to sync');
        return true;
      }

      // Get existing day documents from Firestore
      final daysSnapshot = await _firestoreService.getUserSubcollectionDocuments(
        subcollectionName: 'days',
        orderByField: 'date',
        descending: false,
      );

      // Find modified days that need updating
      final modifiedDays = <DayMealPlan>[];
      final documentIds = <String>[];

      for (final day in filteredDays) {
        // Find existing document for this day
        final existingDoc = daysSnapshot.firstWhere(
          (doc) => _isSameDay((doc['date'] as Timestamp).toDate(), day.date),
          orElse: () => <String, dynamic>{},
        );

        if (existingDoc.isEmpty) {
          // New day - needs to be created
          modifiedDays.add(day);
          documentIds.add(_firestoreService.generateDocumentId('days'));
        } else {
          // Existing day - check if modified
          if (_isDayModified(day, existingDoc)) {
            modifiedDays.add(day);
            documentIds.add(existingDoc['day_document_id'] as String);
          }
        }
      }

      if (modifiedDays.isEmpty) {
        AppLogger.info('MealPlanService: No modified days found, skipping sync');
        return true;
      }

      AppLogger.info('MealPlanService: Found ${modifiedDays.length} modified days to sync');

      // Perform batch update for modified days
      final batch = FirebaseFirestore.instance.batch();
      final userDocRef = FirebaseFirestore.instance.collection('users').doc(_currentUserId);
      final mealsCollectionRef = userDocRef.collection('days');

      // Update each modified day document
      for (int i = 0; i < modifiedDays.length; i++) {
        final day = modifiedDays[i];
        final docId = documentIds[i];
        final dayDocRef = mealsCollectionRef.doc(docId);

        final dayData = {
          'date': Timestamp.fromDate(day.date),
          'meals': day.meals.map((meal) => meal.toJson()).toList(),
          'total_nutrition': day.totalNutrition.toJson(),
          'day_document_id': docId,
          'synced_at': FieldValue.serverTimestamp(),
          'last_modified': FieldValue.serverTimestamp(),
        };

        batch.set(dayDocRef, dayData, SetOptions(merge: true));
      }

      // Update user document metadata timestamp
      final userMetadata = {
        'meal_plan': {
          'last_modified': FieldValue.serverTimestamp(),
        },
      };

      batch.set(userDocRef, userMetadata, SetOptions(merge: true));

      // Commit the batch
      await batch.commit();

      AppLogger.info('MealPlanService: Successfully synced ${modifiedDays.length} modified days to Firestore');

      // Log which days were synced
      for (final day in modifiedDays) {
        AppLogger.info('MealPlanService: Synced day ${day.date.toIso8601String()} with ${day.meals.length} meals');
      }
      return true;
    } catch (e) {
      AppLogger.error('MealPlanService: Error performing incremental sync: $e');
      return false;
    }
  }

  /// Filter days to only include those within sync window (3 past days + today + unlimited future)
  List<DayMealPlan> _filterDaysForSync(List<DayMealPlan> days) {
    final dateRange = _calculateSyncDateRange();
    final startDate = dateRange['startDate']!;

    return days.where((day) {
      return day.date.isAfter(startDate) || _isSameDay(day.date, startDate);
    }).toList();
  }

  /// Check if a day has been modified compared to its Firestore document
  bool _isDayModified(DayMealPlan day, Map<String, dynamic> existingDoc) {
    try {
      // Compare meal count
      final existingMeals = existingDoc['meals'] as List<dynamic>?;
      if (existingMeals == null || existingMeals.length != day.meals.length) {
        return true;
      }

      // Compare meal properties including consumption status
      for (int i = 0; i < day.meals.length; i++) {
        final localMeal = day.meals[i];
        final existingMeal = existingMeals[i] as Map<String, dynamic>;

        // Check meal name
        if (localMeal.name != existingMeal['name']) {
          AppLogger.info('MealPlanService: Day modified - meal name changed: ${existingMeal['name']} -> ${localMeal.name}');
          return true;
        }

        // Check consumption status
        final localIsConsumed = localMeal.isConsumed;
        final existingIsConsumed = existingMeal['is_consumed'] as bool? ?? false;
        if (localIsConsumed != existingIsConsumed) {
          AppLogger.info('MealPlanService: Day modified - consumption status changed for ${localMeal.name}: $existingIsConsumed -> $localIsConsumed');
          return true;
        }

        // Check consumed timestamp
        final localConsumedAt = localMeal.consumedAt?.toIso8601String();
        final existingConsumedAt = existingMeal['consumed_at'] as String?;
        if (localConsumedAt != existingConsumedAt) {
          AppLogger.info('MealPlanService: Day modified - consumed timestamp changed for ${localMeal.name}: $existingConsumedAt -> $localConsumedAt');
          return true;
        }

        // Check selected meal ID (for meal replacements)
        final localSelectedMealId = localMeal.selectedMealId;
        final existingSelectedMealId = existingMeal['selected_meal_id'] as String?;
        if (localSelectedMealId != existingSelectedMealId) {
          return true;
        }
      }

      AppLogger.info('MealPlanService: Day not modified - all meals match existing data');
      return false;
    } catch (e) {
      // If comparison fails, assume modified
      AppLogger.error('MealPlanService: Error comparing day modifications: $e');
      return true;
    }
  }

  /// Perform full sync - clears all existing documents and recreates them
  /// Only syncs days within the sync window (3 past days + today + unlimited future)
  Future<bool> _performFullSync(GeneratedMealPlan generatedMealPlan) async {
    try {
      final batch = FirebaseFirestore.instance.batch();
      final userDocRef = FirebaseFirestore.instance.collection('users').doc(_currentUserId);
      final mealsCollectionRef = userDocRef.collection('days');

      // Clear existing meal plan documents for this user
      await _clearExistingMealPlan();

      // Filter days to only include those within sync window
      final filteredDays = _filterDaysForSync(generatedMealPlan.days);

      AppLogger.info('MealPlanService: Performing full sync for ${filteredDays.length} days within sync window');

      // Create new documents for each day
      for (final day in filteredDays) {
        final docId = _firestoreService.generateDocumentId('days');
        final dayDocRef = mealsCollectionRef.doc(docId);

        final dayData = {
          'date': Timestamp.fromDate(day.date),
          'meals': day.meals.map((meal) => meal.toJson()).toList(),
          'total_nutrition': day.totalNutrition.toJson(),
          'day_document_id': docId,
          'synced_at': FieldValue.serverTimestamp(),
          'last_modified': FieldValue.serverTimestamp(),
        };

        batch.set(dayDocRef, dayData);
      }

      // Update user document metadata
      final userMetadata = {
        'meal_plan': {
          'last_modified': FieldValue.serverTimestamp(),
          'total_days': filteredDays.length,
        },
      };

      batch.set(userDocRef, userMetadata, SetOptions(merge: true));

      // Commit the batch
      await batch.commit();

      AppLogger.info('MealPlanService: Successfully performed full sync for ${filteredDays.length} days');
      return true;
    } catch (e) {
      AppLogger.error('MealPlanService: Error performing full sync: $e');
      return false;
    }
  }

  /// Clear existing meal plan documents from Firestore
  Future<void> _clearExistingMealPlan() async {
    try {
      final daysSnapshot = await _firestoreService.getUserSubcollectionDocuments(
        subcollectionName: 'days',
      );

      if (daysSnapshot.isNotEmpty) {
        final batch = FirebaseFirestore.instance.batch();
        final userDocRef = FirebaseFirestore.instance.collection('users').doc(_currentUserId);
        final mealsCollectionRef = userDocRef.collection('days');

        for (final dayDoc in daysSnapshot) {
          final docId = dayDoc['day_document_id'] as String?;
          if (docId != null) {
            batch.delete(mealsCollectionRef.doc(docId));
          }
        }

        await batch.commit();
        AppLogger.info('MealPlanService: Cleared ${daysSnapshot.length} existing meal plan documents');
      }
    } catch (e) {
      AppLogger.error('MealPlanService: Error clearing existing meal plan: $e');
      // Don't throw error as this shouldn't break the sync operation
    }
  }

}

@riverpod
Future<MealPlanService> mealPlanService(MealPlanServiceRef ref) async {
  final functionsService = await ref.watch(firebaseFunctionsServiceProvider.future);
  final firestoreService = ref.watch(firestoreServiceProvider);
  final localStorageService = await ref.watch(localStorageServiceProvider.future);

  return MealPlanService(
    functionsService,
    firestoreService,
    localStorageService,
  );
}
