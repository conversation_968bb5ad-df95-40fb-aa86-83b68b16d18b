import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../data/services/meal_image_service.dart';
import '../../meal_planning/providers/current_meal_plan_provider.dart';

part 'meal_image_provider.freezed.dart';
part 'meal_image_provider.g.dart';

@freezed
class MealImageState with _$MealImageState {
  const factory MealImageState({
    @Default(false) bool isGenerating,
    @Default(null) String? error,
    @Default(null) String? generatingMealId,
  }) = _MealImageState;
}

@riverpod
class MealImageNotifier extends _$MealImageNotifier {
  StreamSubscription? _imageTrackerSubscription;
  MealImageService? _mealImageService;

  @override
  MealImageState build() {
    // Initialize service
    _initializeService();
    return const MealImageState();
  }

  /// Initialize the meal image service
  Future<void> _initializeService() async {
    try {
      _mealImageService = await ref.read(mealImageServiceProvider.future);
    } catch (e) {
      debugPrint('MealImageNotifier: Error initializing service: $e');
    }
  }

  /// Generate image for a meal
  Future<void> generateMealImage({
    String? dayDocumentId,
    String? mealDate,
    required String mealId,
  }) async {
    try {
      // Ensure service is initialized
      _mealImageService ??= await ref.read(mealImageServiceProvider.future);

      // Set generating state
      state = state.copyWith(
        isGenerating: true,
        error: null,
        generatingMealId: mealId,
      );

      // Start listening for image generation tracker updates
      _startMealImageGenerationListener(mealId);

      // Call service to generate image
      final result = await _mealImageService!.generateMealImage(
        dayDocumentId: dayDocumentId,
        mealDate: mealDate,
        mealId: mealId,
      );

      if (!result.success) {
        throw Exception(result.error ?? 'Failed to generate meal image');
      }

      debugPrint('MealImageNotifier: Image generation started successfully for meal $mealId');
    } catch (e) {
      debugPrint('MealImageNotifier: Error generating meal image: $e');

      // Clear loading state on error
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
        generatingMealId: null,
      );

      // Clear loading flag from service
      await _mealImageService?.clearGenerationLoadingFlag(mealId);
    }
  }

  /// Start listening for image generation tracker updates
  void _startMealImageGenerationListener(String mealId) {
    _imageTrackerSubscription?.cancel();

    if (_mealImageService == null) {
      debugPrint('MealImageNotifier: Service not initialized, cannot start listener');
      return;
    }

    _imageTrackerSubscription = _mealImageService!.createImageGenerationListener(
      mealId,
      _handleMealImageGenerationUpdate,
    );
  }

  /// Handle meal image generation update from background job tracker
  Future<void> _handleMealImageGenerationUpdate(String mealId, Map<String, dynamic> data) async {
    try {
      debugPrint('MealImageNotifier: Handling update for meal $mealId with data: $data');

      final status = data['status'] as String?;
      if (status == null) {
        debugPrint('MealImageNotifier: No status found in document for meal $mealId');
        return;
      }

      debugPrint('MealImageNotifier: Status is $status for meal $mealId');

      if (status == 'success') {
        await _handleSuccessStatus(mealId, data);
      } else if (status == 'fail') {
        await _handleFailedStatus(mealId, data);
      } else if (status == 'in-progress') {
        await _handleInProgressStatus(mealId);
      } else {
        debugPrint('MealImageNotifier: Unknown status $status for meal $mealId');
      }
    } catch (e) {
      debugPrint('MealImageNotifier: Error handling meal image generation update: $e');
    }
  }

  /// Handle success status from background job
  Future<void> _handleSuccessStatus(String mealId, Map<String, dynamic> data) async {
    try {
      final lastCompletionTimestamp = data['last_completion_datetime'] as Timestamp?;
      if (lastCompletionTimestamp == null) {
        debugPrint('MealImageNotifier: No completion datetime found for successful meal $mealId');
        return;
      }

      final completionDateTime = lastCompletionTimestamp.toDate();
      debugPrint('MealImageNotifier: Image generation completed at: ${completionDateTime.toIso8601String()}');

      // Use service to check if meal should be updated
      final shouldUpdate = await _mealImageService?.shouldUpdateMealImage(mealId, completionDateTime) ?? false;
      if (!shouldUpdate) {
        debugPrint('MealImageNotifier: Meal image is already up to date for meal $mealId');
        return;
      }

      // Update the meal from Firestore using service
      final updateResult = await _mealImageService?.updateMealFromFirestore(mealId);
      if (updateResult?.success == true) {
        // Invalidate current meal plan provider to refresh UI
        ref.invalidate(currentMealPlanNotifierProvider);
      }

      // Clear loading flag and stop loading state
      await _mealImageService?.clearGenerationLoadingFlag(mealId);

      state = state.copyWith(
        isGenerating: false,
        error: null,
        generatingMealId: null,
      );

      debugPrint('MealImageNotifier: Successfully completed meal image generation for $mealId');
    } catch (e) {
      debugPrint('MealImageNotifier: Error handling success status for meal $mealId: $e');
    }
  }

  /// Handle failed status from background job
  Future<void> _handleFailedStatus(String mealId, Map<String, dynamic> data) async {
    try {
      final statusMessage = data['status_message'] as String?;
      final errorMessage = statusMessage ?? 'فشل في إنشاء صورة الوجبة';

      // Show error in generation box and stop loading state
      // Keep generatingMealId so we know which meal the error is for
      state = state.copyWith(
        isGenerating: false,
        error: errorMessage,
        generatingMealId: mealId,
      );

      // Clear loading flag using service
      await _mealImageService?.clearGenerationLoadingFlag(mealId);

      debugPrint('MealImageNotifier: Image generation failed for meal $mealId: $errorMessage');
    } catch (e) {
      debugPrint('MealImageNotifier: Error handling failed status for meal $mealId: $e');
    }
  }

  /// Handle in-progress status from background job
  Future<void> _handleInProgressStatus(String mealId) async {
    try {
      // Show loading state and save loading flag
      state = state.copyWith(
        isGenerating: true,
        generatingMealId: mealId,
        error: null,
      );

      // Save loading flag using service
      await _mealImageService?.setGenerationLoadingFlag(mealId);

      debugPrint('MealImageNotifier: Image generation in progress for meal $mealId');
    } catch (e) {
      debugPrint('MealImageNotifier: Error handling in-progress status for meal $mealId: $e');
    }
  }






  /// Check if a specific meal is currently generating an image
  bool isMealGeneratingImage(String mealId) {
    return state.isGenerating && state.generatingMealId == mealId;
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(
      error: null,
      generatingMealId: null,
    );
  }

  /// Initialize image generation loading state from local storage
  Future<void> initializeImageGenerationState(String mealId) async {
    try {
      debugPrint('MealImageNotifier: Initializing image generation state for meal $mealId');

      // Ensure service is initialized
      _mealImageService ??= await ref.read(mealImageServiceProvider.future);

      final hasLoadingFlag = _mealImageService!.hasGenerationLoadingFlag(mealId);

      if (hasLoadingFlag) {
        // Show loading state if flag exists
        state = state.copyWith(
          isGenerating: true,
          generatingMealId: mealId,
          error: null,
        );
        debugPrint('MealImageNotifier: Found loading flag, showing loading state for meal $mealId');
      } else {
        // Stop loading state if no flag exists
        state = state.copyWith(
          isGenerating: false,
          generatingMealId: null,
          error: null,
        );
        debugPrint('MealImageNotifier: No loading flag found, stopping loading state for meal $mealId');
      }

      // Always start the listener to monitor for updates
      _startMealImageGenerationListener(mealId);
    } catch (e) {
      debugPrint('MealImageNotifier: Error initializing image generation state: $e');
    }
  }


  /// Dispose resources
  void dispose() {
    _imageTrackerSubscription?.cancel();
  }
}
