import 'package:cached_network_image/cached_network_image.dart';
import 'package:easydietai/core/theme/app_colors.dart';
import 'package:easydietai/features/meal_planning/data/models/meal_plan_request.dart';
import 'package:easydietai/features/meal_details/providers/meal_image_provider.dart';
import 'package:easydietai/features/meal_details/data/services/meal_image_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Widget for displaying meal image or generate image button
class MealImageWidget extends ConsumerStatefulWidget {
  final GeneratedMeal meal;
  final String dayDocumentId;
  final DateTime mealDate;

  const MealImageWidget({
    required this.meal,
    required this.dayDocumentId,
    required this.mealDate,
    super.key,
  });

  @override
  ConsumerState<MealImageWidget> createState() => _MealImageWidgetState();
}

class _MealImageWidgetState extends ConsumerState<MealImageWidget> {
  MealImageService? _mealImageService;

  @override
  void initState() {
    super.initState();
    // Initialize image generation state when widget is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && widget.meal.id != null) {
        try {
          ref.read(mealImageNotifierProvider.notifier)
              .initializeImageGenerationState(widget.meal.id!);
        } catch (e) {
          debugPrint('Error initializing image generation state: $e');
        }
      }
    });

    // Initialize service
    _initializeService();
  }

  /// Initialize the meal image service
  Future<void> _initializeService() async {
    try {
      _mealImageService = await ref.read(mealImageServiceProvider.future);
    } catch (e) {
      debugPrint('MealImageWidget: Error initializing service: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final mealId = widget.meal.id;

    // Don't show anything if meal doesn't have an ID
    if (mealId == null) return const SizedBox.shrink();

    // Watch the meal image state for reactive updates
    final mealImageState = ref.watch(mealImageNotifierProvider);
    final isGenerating = mealImageState.isGenerating && mealImageState.generatingMealId == mealId;
    final hasError = mealImageState.error != null && mealImageState.generatingMealId == mealId;

    // Get the active meal data (applies meal version selection logic)
    final currentMeal = widget.meal.getActiveMeal();
    final hasValidImage = currentMeal.imageUrl?.isNotEmpty == true &&
                         (_mealImageService?.isValidImageUrl(currentMeal.imageUrl!) ?? true);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: hasValidImage
            ? _buildMealImage(currentMeal.imageUrl!)
            : _buildGenerateImageSection(isGenerating, hasError, mealImageState.error),
      ),
    );
  }



  /// Build the meal image display
  Widget _buildMealImage(String imageUrl) {
    return GestureDetector(
      onTap: () => _showFullScreenImage(imageUrl),
      child: AspectRatio(
        aspectRatio: 2.5 / 1,
        child: Stack(
          children: [
            // Main image
            _buildImageWidget(imageUrl),
            // Gradient overlay for better text readability
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
              ),
            ),
            // Image generated indicator
            Positioned(
              bottom: 12,
              left: 12,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.auto_awesome,
                      size: 14,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'تم إنشاؤها بالذكاء الاصطناعي',
                      style: TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show full screen image viewer
  void _showFullScreenImage(String imageUrl) {
    showDialog<void>(
      context: context,
      barrierColor: Colors.black87,
      builder: (context) => Dialog.fullscreen(
        backgroundColor: Colors.black87,
        child: Stack(
          children: [
            // Full screen image
            Center(
              child: InteractiveViewer(
                minScale: 0.5,
                maxScale: 5.0,
                child: _buildImageWidget(imageUrl, fit: BoxFit.fitWidth),
              ),
            ),
            // Close button
            Positioned(
              top: 50,
              right: 20,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 30,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.black54,
                  shape: const CircleBorder(),
                ),
              ),
            ),
            // Image info overlay
            Positioned(
              bottom: 50,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.auto_awesome,
                          color: Colors.white,
                          size: 16,
                        ),
                        SizedBox(width: 8),
                        Text(
                          'صورة تم إنشاؤها بالذكاء الاصطناعي',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Text(
                      'اضغط واسحب للتكبير والتصغير',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build image widget with fallback handling
  Widget _buildImageWidget(String imageUrl, {BoxFit fit = BoxFit.cover}) {
    // Try to convert URL format using service if available
    final convertedUrl = _mealImageService?.convertImageUrlFormat(imageUrl) ?? imageUrl;

    debugPrint('MealImageWidget: Loading image with URL: $convertedUrl');

    return CachedNetworkImage(
      imageUrl: convertedUrl,
      width: double.infinity,
      height: double.infinity,
      fit: fit,
      placeholder: (context, url) => Container(
        color: Colors.grey[200],
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
      errorWidget: (context, url, error) {
        debugPrint('MealImageWidget: Image loading error for URL: $url');
        debugPrint('MealImageWidget: Error details: $error');
        debugPrint('MealImageWidget: Original URL: $imageUrl');
        debugPrint('MealImageWidget: Converted URL: $convertedUrl');
        return _buildImageErrorWidget();
      },
      httpHeaders: const {
        'User-Agent': 'Mozilla/5.0 (compatible; EasyDietAI/1.0)',
      },
    );
  }

  /// Build error widget when image fails to load
  Widget _buildImageErrorWidget() {
    return Container(
      color: Colors.grey[200],
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image,
              size: 48,
              color: Colors.grey,
            ),
            SizedBox(height: 8),
            Text(
              'فشل في تحميل الصورة',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the generate image section
  Widget _buildGenerateImageSection(bool isGenerating, bool hasError, String? errorMessage) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: hasError
              ? [
                  Colors.red.withOpacity(0.1),
                  Colors.red.withOpacity(0.05),
                ]
              : [
                  AppColors.primary.withOpacity(0.1),
                  AppColors.secondary.withOpacity(0.1),
                ],
        ),
      ),
      child: Center(
        child: isGenerating
            ? _buildGeneratingState()
            : hasError
                ? _buildErrorState(errorMessage)
                : _buildGenerateButton(),
      ),
    );
  }

  /// Build the generating state UI
  Widget _buildGeneratingState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 32,
          height: 32,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ),
        const SizedBox(height: 12),
        Text(
          'جاري إنشاء صورة الوجبة...',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'قد يستغرق هذا بضع دقائق',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// Build the error state UI
  Widget _buildErrorState(String? errorMessage) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.error_outline,
          size: 32,
          color: Colors.red[600],
        ),
        const SizedBox(height: 12),
        Text(
          errorMessage ?? 'فشل في إنشاء صورة الوجبة',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.red[600],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _handleRetryGenerateImage,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.red[600],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'إعادة المحاولة',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build the generate image button
  Widget _buildGenerateButton() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _handleGenerateImage,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.auto_awesome,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'إنشاء صورة للوجبة',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Handle retry generate image button press
  Future<void> _handleRetryGenerateImage() async {
    final mealId = widget.meal.id;
    if (mealId == null) return;

    // Clear the error state first
    ref.read(mealImageNotifierProvider.notifier).clearError();

    // Then trigger generation
    await _handleGenerateImage();
  }

  /// Handle generate image button press
  Future<void> _handleGenerateImage() async {
    final mealId = widget.meal.id;
    if (mealId == null) return;

    try {
      // Format meal date as YYYY-MM-DD
      final mealDateString = widget.mealDate.toIso8601String().split('T')[0];

      await ref.read(mealImageNotifierProvider.notifier).generateMealImage(
        dayDocumentId: widget.dayDocumentId.isNotEmpty ? widget.dayDocumentId : null,
        mealDate: mealDateString,
        mealId: mealId,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم بدء إنشاء صورة الوجبة. ستصلك إشعار عند الانتهاء.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء إنشاء صورة الوجبة: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }
}
