import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/core/theme/app_colors.dart';
import 'package:easydietai/features/meal_planning/providers/current_meal_plan_provider.dart';
import 'package:easydietai/features/shopping_list/providers/shopping_list_generation_provider.dart';
import 'package:easydietai/features/shopping_list/providers/shopping_lists_provider.dart';

class GenerateShoppingListCard extends ConsumerStatefulWidget {
  final VoidCallback? onBackPressed;

  const GenerateShoppingListCard({
    super.key,
    this.onBackPressed,
  });

  @override
  ConsumerState<GenerateShoppingListCard> createState() => _GenerateShoppingListCardState();
}

class _GenerateShoppingListCardState extends ConsumerState<GenerateShoppingListCard> {
  bool _isGeneratingShoppingList = false;
  bool _isBackgroundLoading = false;

  @override
  void initState() {
    super.initState();
    _checkBackgroundLoading();
  }

  /// Check if shopping list generation is currently running in background
  Future<void> _checkBackgroundLoading() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final isLoading = localStorageService.isGeneratingShoppingList();

      if (mounted) {
        setState(() {
          _isBackgroundLoading = isLoading;
        });
      }
    } catch (e) {
      // Ignore errors in checking background loading
    }
  }

  /// Check if there are enough meal days to generate a shopping list
  bool _checkInsufficientMealDays(CurrentMealPlanState mealPlanState) {
    final currentPlan = mealPlanState.currentPlan;
    final mealPlan = currentPlan?.mealPlan;
    if (currentPlan == null || mealPlan == null || mealPlan.days.isEmpty) {
      return true; // No days exist, insufficient
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Count future days (today and beyond)
    final futureDays = mealPlan.days.where((day) {
      final dayDate = DateTime(day.date.year, day.date.month, day.date.day);
      return dayDate.isAtSameMomentAs(today) || dayDate.isAfter(today);
    }).toList();

    // Need at least 2 days to generate a meaningful shopping list
    return futureDays.length < 2;
  }

  @override
  Widget build(BuildContext context) {
    // Watch for meal plan state changes (including errors)
    final mealPlanState = ref.watch(currentMealPlanNotifierProvider);
    final shoppingListGenerationState = ref.watch(shoppingListGenerationNotifierProvider);

    // Check for errors from both providers
    final hasMealPlanError = mealPlanState.error != null;
    final hasShoppingListGenerationError = shoppingListGenerationState.hasError;
    final hasError = hasMealPlanError || hasShoppingListGenerationError;

    // Get the error message from the appropriate provider
    final errorMessage = hasShoppingListGenerationError
        ? shoppingListGenerationState.errorMessage
        : mealPlanState.error;

    // Check if we have insufficient meal days
    final hasInsufficientMealDays = _checkInsufficientMealDays(mealPlanState);

    // Check if shopping list generation is loading (for button-level loading)
    final isShoppingListGenerationLoading = shoppingListGenerationState.isLoading;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange.shade400, Colors.orange.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.shopping_cart_outlined,
                size: 40,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              'إنشاء قائمة تسوق',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Description
            Text(
              'هل تريد إنشاء قائمة تسوق بناءً على خطة الوجبات الخاصة بك؟\nسيتم تجميع جميع المكونات المطلوبة لك.',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                    height: 1.5,
                  ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // Error Display
            if (hasError) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red.shade600,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'خطأ في إنشاء قائمة التسوق',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.red.shade700,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      errorMessage ?? 'حدث خطأ غير متوقع',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.red.shade700,
                            height: 1.4,
                          ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Insufficient Meal Days Warning
            if (hasInsufficientMealDays) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.restaurant_menu,
                          color: Colors.orange.shade600,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'تحتاج إلى المزيد من الوجبات',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade700,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لإنشاء قائمة تسوق مفيدة، تحتاج إلى وجبات مخططة لعدة أيام قادمة. يُنصح بإنشاء خطة وجبات أولاً أو إضافة المزيد من الأيام.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.orange.shade700,
                            height: 1.4,
                          ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Generate Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: ((!hasError && (_isGeneratingShoppingList || _isBackgroundLoading || isShoppingListGenerationLoading)) || hasInsufficientMealDays)
                    ? null
                    : hasError
                        ? _handleRetryFromMainButton
                        : _showConfirmationBottomSheet,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: (!hasError && (_isGeneratingShoppingList || _isBackgroundLoading || isShoppingListGenerationLoading))
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : hasError
                        ? Icon(Icons.refresh)
                        : hasInsufficientMealDays
                            ? Icon(Icons.restaurant_menu)
                            : Icon(Icons.shopping_cart),
                label: Text(
                  (!hasError && (_isGeneratingShoppingList || _isBackgroundLoading || isShoppingListGenerationLoading))
                      ? isShoppingListGenerationLoading
                          ? 'جاري الإنشاء في الخلفية...'
                          : _isBackgroundLoading
                              ? 'جاري الإنشاء في الخلفية...'
                              : 'جاري الإنشاء...'
                      : hasError
                          ? 'إعادة المحاولة'
                          : hasInsufficientMealDays
                              ? 'أنشئ خطة وجبات أولاً'
                              : 'إنشاء قائمة تسوق',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Cancel/Back Button
            if (widget.onBackPressed != null)
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _isGeneratingShoppingList
                      ? null
                      : widget.onBackPressed,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.textSecondary,
                    side: BorderSide(color: AppColors.textSecondary),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: Icon(Icons.arrow_back),
                  label: Text('العودة لقوائم التسوق'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Show confirmation bottom sheet before generating shopping list
  void _showConfirmationBottomSheet() {
    // For now, directly generate the shopping list
    // In the future, this could show a confirmation dialog
    _generateShoppingList();
  }

  /// Handle retry from the main button when there's an error
  Future<void> _handleRetryFromMainButton() async {
    try {
      final shoppingListGenerationState = ref.read(shoppingListGenerationNotifierProvider);

      // Clear the error from the shopping list generation provider
      if (shoppingListGenerationState.hasError) {
        await ref.read(shoppingListGenerationNotifierProvider.notifier).retryGeneration();
        // Also trigger the actual shopping list generation
        await ref.read(shoppingListsNotifierProvider.notifier).generateNewShoppingList();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إعادة المحاولة: $e'),
            backgroundColor: Colors.red.shade600,
          ),
        );
      }
    }
  }

  /// Generate shopping list
  Future<void> _generateShoppingList() async {
    setState(() {
      _isGeneratingShoppingList = true;
    });

    try {
      await ref.read(shoppingListsNotifierProvider.notifier).generateNewShoppingList();

      // Check if background loading started
      await _checkBackgroundLoading();

      // Show appropriate message based on whether background processing started
      if (mounted) {
        if (_isBackgroundLoading) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم بدء إنشاء قائمة التسوق في الخلفية، سيتم تحديث القائمة تلقائياً عند الانتهاء'),
              backgroundColor: Colors.orange.shade600,
              duration: Duration(seconds: 4),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم إنشاء قائمة التسوق بنجاح!'),
              backgroundColor: Colors.green.shade600,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إنشاء قائمة التسوق: $e'),
            backgroundColor: Colors.red.shade600,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingShoppingList = false;
        });
      }
    }
  }
}
