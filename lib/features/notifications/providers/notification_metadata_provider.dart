import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/utils/logger.dart';
import '../../../shared/providers/app_state_provider.dart';

class NotificationMetadata {
  final int totalCount;
  final int unseenCount;

  const NotificationMetadata({
    this.totalCount = 0,
    this.unseenCount = 0,
  });

  factory NotificationMetadata.fromJson(Map<String, dynamic> json) {
    return NotificationMetadata(
      totalCount: json['total_count'] as int? ?? 0,
      unseenCount: json['unseen_count'] as int? ?? 0,
    );
  }

  NotificationMetadata copyWith({
    int? totalCount,
    int? unseenCount,
  }) {
    return NotificationMetadata(
      totalCount: totalCount ?? this.totalCount,
      unseenCount: unseenCount ?? this.unseenCount,
    );
  }
}

class NotificationMetadataNotifier extends StateNotifier<NotificationMetadata> {
  StreamSubscription<DocumentSnapshot>? _metadataSubscription;
  final Ref _ref;

  NotificationMetadataNotifier(this._ref) : super(const NotificationMetadata()) {
    // Don't start listening immediately - wait for auth state to be ready
  }

  @override
  void dispose() {
    _metadataSubscription?.cancel();
    super.dispose();
  }

  /// Start listening to notification metadata changes
  void startListening() {
    final appState = _ref.read(appStateNotifierProvider);

    if (appState.authStatus != AuthStatus.authenticated || appState.user == null) {
      AppLogger.warning('NotificationMetadataNotifier: User not authenticated');
      return;
    }

    final user = appState.user!;
    AppLogger.info('NotificationMetadataNotifier: Starting metadata listener for user ${user.uid}');

    // New structure: users/{userId}/notifications/meta
    _metadataSubscription = FirebaseFirestore.instance
        .collection('users')
        .doc(user.uid)
        .collection('notifications')
        .doc('meta')
        .snapshots()
        .listen(
          _handleMetadataUpdate,
          onError: _handleError,
        );
  }

  /// Handle metadata updates from Firestore
  void _handleMetadataUpdate(DocumentSnapshot snapshot) {
    try {
      if (!snapshot.exists) {
        AppLogger.warning('NotificationMetadataNotifier: Notification metadata document does not exist');
        // Use defaults when metadata document doesn't exist
        state = const NotificationMetadata();
        return;
      }

      final data = snapshot.data() as Map<String, dynamic>?;

      if (data != null) {
        final metadata = NotificationMetadata(
          totalCount: data['total_count'] as int? ?? 0,
          unseenCount: data['unseen_count'] as int? ?? 0,
        );

        AppLogger.info('NotificationMetadataNotifier: Updated metadata - total: ${metadata.totalCount}, unseen: ${metadata.unseenCount}');
        state = metadata;
      } else {
        // No metadata found, use defaults
        AppLogger.info('NotificationMetadataNotifier: No notification metadata found, using defaults');
        state = const NotificationMetadata();
      }
    } catch (e) {
      AppLogger.error('NotificationMetadataNotifier: Error processing metadata update: $e');
    }
  }

  /// Handle errors from the Firestore listener
  void _handleError(Object error) {
    AppLogger.error('NotificationMetadataNotifier: Firestore listener error: $error');
  }

  /// Restart the listener (useful when user authentication changes)
  void restartListener() {
    _metadataSubscription?.cancel();
    startListening();
  }

  /// Stop the listener
  void stopListener() {
    _metadataSubscription?.cancel();
    AppLogger.info('NotificationMetadataNotifier: Stopped metadata listener');
  }

  /// Manually update unseen count (for local state management)
  void updateUnseenCount(int newCount) {
    state = state.copyWith(unseenCount: newCount);
  }

  /// Decrement unseen count by 1 (when marking notification as seen)
  void decrementUnseenCount() {
    if (state.unseenCount > 0) {
      state = state.copyWith(unseenCount: state.unseenCount - 1);
    }
  }
}

/// Provider for unseen notification count from metadata
final unseenNotificationCountFromMetadataProvider = Provider<int>((ref) {
  final metadata = ref.watch(notificationMetadataNotifierProvider);
  return metadata.unseenCount;
});

/// Provider for total notification count from metadata
final totalNotificationCountFromMetadataProvider = Provider<int>((ref) {
  final metadata = ref.watch(notificationMetadataNotifierProvider);
  return metadata.totalCount;
});

/// Provider for checking if notification badge should be shown based on metadata
final shouldShowNotificationBadgeFromMetadataProvider = Provider<bool>((ref) {
  final unseenCount = ref.watch(unseenNotificationCountFromMetadataProvider);
  return unseenCount > 0;
});

/// Main provider for notification metadata notifier
final notificationMetadataNotifierProvider = StateNotifierProvider<NotificationMetadataNotifier, NotificationMetadata>((ref) {
  return NotificationMetadataNotifier(ref);
});

/// Provider to manage notification metadata listener lifecycle based on auth state
final notificationMetadataListenerProvider = Provider<void>((ref) {
  final appState = ref.watch(appStateNotifierProvider);
  final metadataNotifier = ref.read(notificationMetadataNotifierProvider.notifier);

  // Start listening when user is authenticated (don't wait for profile to be loaded)
  if (appState.authStatus == AuthStatus.authenticated && appState.user != null) {
    // User is authenticated, start listening
    AppLogger.info('NotificationMetadataListenerProvider: User authenticated, starting metadata listener');
    metadataNotifier.startListening();
  } else {
    // User is not authenticated, stop listening
    AppLogger.info('NotificationMetadataListenerProvider: User not authenticated, stopping metadata listener');
    metadataNotifier.stopListener();
  }

  // Clean up when provider is disposed
  ref.onDispose(() {
    metadataNotifier.stopListener();
  });
});
