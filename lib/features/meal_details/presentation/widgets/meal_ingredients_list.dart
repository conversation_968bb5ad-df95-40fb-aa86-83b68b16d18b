import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../meal_planning/data/models/meal_plan_request.dart';

/// Widget for displaying meal ingredients with expandable preparation instructions
class MealIngredientsList extends StatefulWidget {
  final GeneratedMeal meal;

  const MealIngredientsList({
    super.key,
    required this.meal,
  });

  @override
  State<MealIngredientsList> createState() => _MealIngredientsListState();
}

class _MealIngredientsListState extends State<MealIngredientsList> {
  /// Track which ingredients are expanded to show instructions
  final Set<String> _expandedIngredients = {};

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: Column(
        key: ValueKey(widget.meal.name),
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مكونات الوجبة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
          ),
          const SizedBox(height: 12),
          ...widget.meal.ingredients.asMap().entries.map((entry) {
            final index = entry.key;
            final ingredient = entry.value;
            return AnimatedContainer(
              duration: Duration(milliseconds: 200 + (index * 50)),
              curve: Curves.easeOutBack,
              child: _buildIngredientTile(ingredient),
            );
          }),
        ],
      ),
    );
  }

  /// Build individual ingredient tile with expandable instructions
  Widget _buildIngredientTile(MealIngredient ingredient) {
    final hasInstructions = ingredient.instructions.isNotEmpty;
    final isExpanded = _expandedIngredients.contains(ingredient.name);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.restaurant,
                color: AppColors.primary,
                size: 20,
              ),
            ),
            title: Text(
              ingredient.name,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            trailing: hasInstructions
                ? IconButton(
                    icon: Icon(
                      isExpanded ? Icons.help : Icons.help_outline,
                      color: AppColors.primary,
                    ),
                    onPressed: () => _toggleIngredientExpansion(ingredient.name),
                  )
                : null,
          ),
          if (hasInstructions && isExpanded) ...[
            const Divider(height: 1),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'طريقة التحضير:',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...ingredient.instructions.map((instruction) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '• ',
                              style: TextStyle(
                                color: AppColors.textSecondary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                instruction,
                                style: TextStyle(
                                  color: AppColors.textSecondary,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Toggle expansion state for ingredient instructions
  void _toggleIngredientExpansion(String ingredientName) {
    setState(() {
      if (_expandedIngredients.contains(ingredientName)) {
        _expandedIngredients.remove(ingredientName);
      } else {
        _expandedIngredients.add(ingredientName);
      }
    });
  }
}
