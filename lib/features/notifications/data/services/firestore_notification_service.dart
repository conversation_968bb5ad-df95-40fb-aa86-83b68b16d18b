import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/utils/logger.dart';
import '../../../../core/utils/timestamp_utils.dart';
import '../models/notification_models.dart';

/// Service to load notifications directly from Firestore
class FirestoreNotificationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Load notifications from Firestore with pagination
  Future<List<AppNotification>> loadNotifications({
    int page = 1,
    int pageSize = 20,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        AppLogger.warning('FirestoreNotificationService: No authenticated user');
        return [];
      }

      AppLogger.info('FirestoreNotificationService: Loading notifications page $page for user ${user.uid}');

      // New structure: users/{userId}/notifications/history/items/{notificationId}
      Query query = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .doc('history')
          .collection('items')
          .orderBy('timestamp', descending: true)
          .limit(pageSize);

      // Add pagination cursor if provided
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final querySnapshot = await query.get();
      
      if (querySnapshot.docs.isEmpty) {
        AppLogger.info('FirestoreNotificationService: No notifications found');
        return [];
      }

      final notifications = <AppNotification>[];
      
      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          
          // Convert Firestore timestamp to DateTime string
          if (data['timestamp'] != null) {
            data['timestamp'] = TimestampUtils.convertTimestampToString(data['timestamp']);
          }
          
          // Ensure the document ID is included
          data['id'] = doc.id;
          
          final notification = AppNotification.fromJson(data);
          notifications.add(notification);
          
          AppLogger.debug('FirestoreNotificationService: Loaded notification ${notification.id}');
        } catch (e) {
          AppLogger.error('FirestoreNotificationService: Error parsing notification ${doc.id}: $e');
        }
      }

      AppLogger.info('FirestoreNotificationService: Successfully loaded ${notifications.length} notifications');
      return notifications;
    } catch (e) {
      AppLogger.error('FirestoreNotificationService: Error loading notifications: $e');
      rethrow;
    }
  }

  /// Load all notifications (for initial load)
  Future<List<AppNotification>> loadAllNotifications() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        AppLogger.warning('FirestoreNotificationService: No authenticated user');
        return [];
      }

      AppLogger.info('FirestoreNotificationService: Loading all notifications for user ${user.uid}');

      // New structure: users/{userId}/notifications/history/items/{notificationId}
      final querySnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .doc('history')
          .collection('items')
          .orderBy('timestamp', descending: true)
          .limit(100) // Reasonable limit for initial load
          .get();

      if (querySnapshot.docs.isEmpty) {
        AppLogger.info('FirestoreNotificationService: No notifications found');
        return [];
      }

      final notifications = <AppNotification>[];
      
      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data() as Map<String, dynamic>;
          
          // Convert Firestore timestamp to DateTime string
          if (data['timestamp'] != null) {
            data['timestamp'] = TimestampUtils.convertTimestampToString(data['timestamp']);
          }
          
          // Ensure the document ID is included
          data['id'] = doc.id;
          
          final notification = AppNotification.fromJson(data);
          notifications.add(notification);
        } catch (e) {
          AppLogger.error('FirestoreNotificationService: Error parsing notification ${doc.id}: $e');
        }
      }

      AppLogger.info('FirestoreNotificationService: Successfully loaded ${notifications.length} notifications');
      return notifications;
    } catch (e) {
      AppLogger.error('FirestoreNotificationService: Error loading all notifications: $e');
      rethrow;
    }
  }

  /// Mark notification as seen in Firestore
  Future<void> markNotificationAsSeen(String notificationId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        AppLogger.warning('FirestoreNotificationService: No authenticated user');
        return;
      }

      AppLogger.info('FirestoreNotificationService: Marking notification $notificationId as seen');

      // New structure: users/{userId}/notifications/history/items/{notificationId}
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .doc('history')
          .collection('items')
          .doc(notificationId)
          .update({'seen': true});

      // Also update the notification metadata to decrement unseen count
      await _updateNotificationMetadata(user.uid, decrementUnseen: true);

      AppLogger.info('FirestoreNotificationService: Successfully marked notification as seen');
    } catch (e) {
      AppLogger.error('FirestoreNotificationService: Error marking notification as seen: $e');
      rethrow;
    }
  }

  /// Update notification metadata in notification meta document
  Future<void> _updateNotificationMetadata(String userId, {bool decrementUnseen = false}) async {
    try {
      // New structure: users/{userId}/notifications/meta
      final metaDocRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('notifications')
          .doc('meta');

      final metaDoc = await metaDocRef.get();
      final currentMeta = metaDoc.exists ? metaDoc.data() as Map<String, dynamic>? ??
          {'total_count': 0, 'unseen_count': 0} : {'total_count': 0, 'unseen_count': 0};

      if (decrementUnseen && (currentMeta['unseen_count'] as int? ?? 0) > 0) {
        final updatedMeta = {
          'total_count': currentMeta['total_count'] as int? ?? 0,
          'unseen_count': (currentMeta['unseen_count'] as int? ?? 0) - 1,
        };

        await metaDocRef.set(updatedMeta, SetOptions(merge: true));

        AppLogger.info('FirestoreNotificationService: Updated notification metadata: $updatedMeta');
      }
    } catch (e) {
      AppLogger.error('FirestoreNotificationService: Error updating notification metadata: $e');
    }
  }

  /// Get notification statistics from Firestore
  Future<NotificationStats> getNotificationStats() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        AppLogger.warning('FirestoreNotificationService: No authenticated user');
        return const NotificationStats();
      }

      // Get stats from notification metadata document
      // New structure: users/{userId}/notifications/meta
      final metaDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .doc('meta')
          .get();

      final metaData = metaDoc.exists ? metaDoc.data() as Map<String, dynamic>? ??
          {'total_count': 0, 'unseen_count': 0} : {'total_count': 0, 'unseen_count': 0};

      final stats = NotificationStats(
        totalCount: metaData['total_count'] as int? ?? 0,
        unseenCount: metaData['unseen_count'] as int? ?? 0,
        todayCount: 0, // We can calculate this later if needed
      );

      AppLogger.info('FirestoreNotificationService: Loaded stats: $stats');
      return stats;
    } catch (e) {
      AppLogger.error('FirestoreNotificationService: Error loading stats: $e');
      return const NotificationStats();
    }
  }

  /// Delete a notification from Firestore
  Future<void> deleteNotification(String notificationId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        AppLogger.warning('FirestoreNotificationService: No authenticated user');
        return;
      }

      AppLogger.info('FirestoreNotificationService: Deleting notification $notificationId');

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('notifications')
          .doc(notificationId)
          .delete();

      AppLogger.info('FirestoreNotificationService: Successfully deleted notification');
    } catch (e) {
      AppLogger.error('FirestoreNotificationService: Error deleting notification: $e');
      rethrow;
    }
  }
}

/// Provider for the Firestore notification service
final firestoreNotificationServiceProvider = Provider<FirestoreNotificationService>((ref) {
  return FirestoreNotificationService();
});
