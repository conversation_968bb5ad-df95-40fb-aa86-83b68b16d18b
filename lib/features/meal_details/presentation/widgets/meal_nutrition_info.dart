import 'package:flutter/material.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../meal_planning/data/models/meal_plan_request.dart';

/// Widget for displaying meal nutrition information with progress bars and macronutrients breakdown
class MealNutritionInfo extends StatelessWidget {
  final GeneratedMeal meal;
  final NutritionInfo? totalDayNutrition;

  const MealNutritionInfo({
    super.key,
    required this.meal,
    this.totalDayNutrition,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: Column(
        key: ValueKey(meal.name),
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الغذائية',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.border),
            ),
            child: Column(
              children: [
                // Calories bar (only show if total day nutrition is provided)
                if (totalDayNutrition != null) ...[
                  _buildNutritionBar(
                    'السعرات الحرارية',
                    meal.nutrition.calories.toDouble(),
                    totalDayNutrition!.calories.toDouble(),
                    Colors.orange,
                    '${meal.nutrition.calories} من ${totalDayNutrition!.calories} سعرة',
                  ),
                  const SizedBox(height: 16),
                ],
                // Macronutrients stacked bar
                _buildMacronutrientsBar(meal.nutrition),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build nutrition progress bar
  Widget _buildNutritionBar(String label, double value, double maxValue, Color color, String displayText) {
    final percentage = (value / maxValue).clamp(0.0, 1.0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            Text(
              displayText,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(4),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: percentage,
            child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build macronutrients stacked bar with legend
  Widget _buildMacronutrientsBar(NutritionInfo nutrition) {
    final totalMacros = nutrition.protein + nutrition.carbs + nutrition.fat;
    final proteinPercentage = totalMacros > 0 ? nutrition.protein / totalMacros : 0.0;
    final carbsPercentage = totalMacros > 0 ? nutrition.carbs / totalMacros : 0.0;
    final fatPercentage = totalMacros > 0 ? nutrition.fat / totalMacros : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'توزيع المغذيات الكبرى',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 12,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            children: [
              if (proteinPercentage > 0)
                Expanded(
                  flex: (proteinPercentage * 100).round(),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(6),
                        bottomRight: Radius.circular(6),
                        topLeft: carbsPercentage == 0 && fatPercentage == 0 ? Radius.circular(6) : Radius.zero,
                        bottomLeft: carbsPercentage == 0 && fatPercentage == 0 ? Radius.circular(6) : Radius.zero,
                      ),
                    ),
                  ),
                ),
              if (carbsPercentage > 0)
                Expanded(
                  flex: (carbsPercentage * 100).round(),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.only(
                        topLeft: proteinPercentage == 0 ? Radius.circular(6) : Radius.zero,
                        bottomLeft: proteinPercentage == 0 ? Radius.circular(6) : Radius.zero,
                        topRight: fatPercentage == 0 ? Radius.circular(6) : Radius.zero,
                        bottomRight: fatPercentage == 0 ? Radius.circular(6) : Radius.zero,
                      ),
                    ),
                  ),
                ),
              if (fatPercentage > 0)
                Expanded(
                  flex: (fatPercentage * 100).round(),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.only(
                        topRight: proteinPercentage == 0 && carbsPercentage == 0 ? Radius.circular(6) : Radius.zero,
                        bottomRight: proteinPercentage == 0 && carbsPercentage == 0 ? Radius.circular(6) : Radius.zero,
                        topLeft: Radius.circular(6),
                        bottomLeft: Radius.circular(6),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildMacroLegend('البروتين', '${nutrition.protein.toStringAsFixed(1)}جم', Colors.blue),
            _buildMacroLegend('الكربوهيدرات', '${nutrition.carbs.toStringAsFixed(1)}جم', Colors.green),
            _buildMacroLegend('الدهون', '${nutrition.fat.toStringAsFixed(1)}جم', Colors.red),
          ],
        ),
      ],
    );
  }

  /// Build macro legend item
  Widget _buildMacroLegend(String label, String value, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
