import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:easydietai/core/services/firestore_service.dart';
import 'package:easydietai/features/shopping_list/providers/shopping_lists_provider.dart';
import 'package:easydietai/features/shopping_list/data/services/shopping_list_sync_service.dart';
import 'package:easydietai/features/shopping_list/data/models/shopping_list_models.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_lists_loading_widget.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_lists_empty_widget.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_lists_generating_indicator.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_list_card_widget.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_lists_error_toast.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_lists_dialogs.dart';

class ShoppingListsPage extends ConsumerStatefulWidget {
  const ShoppingListsPage({super.key});

  @override
  ConsumerState<ShoppingListsPage> createState() => _ShoppingListsPageState();
}

class _ShoppingListsPageState extends ConsumerState<ShoppingListsPage> {

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeShoppingListLoadingState();
      ref.read(shoppingListsNotifierProvider.notifier).loadShoppingLists();
    });
  }

  /// Initialize shopping list loading state by checking local storage and setting up Firestore listener
  Future<void> _initializeShoppingListLoadingState() async {
    try {
      final shoppingListSyncService = ref.read(shoppingListSyncServiceProvider);

      // Check if there's a local loading flag
      final hasLoadingFlag = shoppingListSyncService.isGenerating();

      if (hasLoadingFlag) {
        // Set loading state to true via the provider
        ref.read(shoppingListsNotifierProvider.notifier).setBackgroundGeneratingState(true);

        // Initialize Firestore listener to detect completion
        await _initializeFirestoreListener();
      } else {
        // Ensure loading state is off
        ref.read(shoppingListsNotifierProvider.notifier).setBackgroundGeneratingState(false);
      }

      debugPrint('ShoppingListsPage: Initialized shopping list loading state: $hasLoadingFlag');
    } catch (e) {
      debugPrint('ShoppingListsPage: Error initializing shopping list loading state: $e');
      // If there's an error checking the flag, ensure loading state is off
      ref.read(shoppingListsNotifierProvider.notifier).setBackgroundGeneratingState(false);
    }
  }

  /// Initialize Firestore listener for shopping list generation completion
  Future<void> _initializeFirestoreListener() async {
    try {
      final firestoreService = ref.read(firestoreServiceProvider);
      final userId = firestoreService.currentUserId;

      if (userId == null) {
        debugPrint('ShoppingListsPage: No user available for Firestore listener');
        return;
      }

      debugPrint('ShoppingListsPage: Setting up Firestore listener for shopping list generation');

      // Listen to bjt_single/bjt_shopping_list document for completion
      FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .collection('bjt_single')
          .doc('bjt_shopping_list')
          .snapshots()
          .listen(
        _handleBackgroundJobUpdate,
        onError: (Object error) {
          debugPrint('ShoppingListsPage: Firestore listener error: $error');
        },
      );
    } catch (e) {
      debugPrint('ShoppingListsPage: Error setting up Firestore listener: $e');
    }
  }

  /// Handle background job updates from Firestore
  Future<void> _handleBackgroundJobUpdate(DocumentSnapshot snapshot) async {
    try {
      if (!snapshot.exists) return;

      final data = snapshot.data() as Map<String, dynamic>?;
      if (data == null) return;

      debugPrint('ShoppingListsPage: Received bjt_single update: ${data.keys}');

      // Check if the job is completed
      final completedAtTimestamp = data['last_completion_datetime'] as Timestamp?;
      if (completedAtTimestamp == null) {
        debugPrint('ShoppingListsPage: Shopping list generation not completed yet');
        return;
      }

      final completedAt = completedAtTimestamp.toDate();
      final shoppingListSyncService = ref.read(shoppingListSyncServiceProvider);

      // Get local loading flag timestamp
      final localFlagTimestamp = shoppingListSyncService.getGenerationFlagTimestamp();
      if (localFlagTimestamp == null) {
        debugPrint('ShoppingListsPage: No local loading flag found');
        return;
      }

      final localFlagTime = DateTime.fromMillisecondsSinceEpoch(localFlagTimestamp);

      // Only process if Firestore completion is after local flag
      if (!completedAt.isAfter(localFlagTime)) {
        debugPrint('ShoppingListsPage: Firestore completion is not newer than local flag');
        return;
      }

      // Clear the local loading flag
      await shoppingListSyncService.removeGeneratingFlag();
      debugPrint('ShoppingListsPage: Cleared local loading flag');

      // Update UI state
      ref.read(shoppingListsNotifierProvider.notifier).setBackgroundGeneratingState(false);

      // Check job status
      final status = data['status'] as String?;
      if (status == 'success') {
        // Refresh shopping lists to get the new data
        await ref.read(shoppingListsNotifierProvider.notifier).refreshShoppingLists();
        debugPrint('ShoppingListsPage: Shopping list generation completed successfully');
      } else {
        // Handle failure case
        final failureMessage = data['status_message'] as String? ?? 'حدث خطأ أثناء إنشاء قائمة التسوق';
        debugPrint('ShoppingListsPage: Shopping list generation failed: $failureMessage');

        // The provider will handle error state through the refreshShoppingLists method
        // or we can trigger a reload to show the error state
        await ref.read(shoppingListsNotifierProvider.notifier).loadShoppingLists();
      }
    } catch (e) {
      debugPrint('ShoppingListsPage: Error handling background job update: $e');
    }
  }

  /// Refresh shopping lists from Firestore and update local storage
  Future<void> _refreshShoppingLists() async {
    await ref.read(shoppingListsNotifierProvider.notifier).refreshShoppingLists();
  }

  @override
  Widget build(BuildContext context) {
    final overviewState = ref.watch(shoppingListsNotifierProvider);
    final isLoading = ref.watch(isShoppingListsLoadingProvider);
    final isGenerating = ref.watch(isShoppingListGeneratingProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('قوائم التسوق'),
        backgroundColor: Colors.orange.shade50,
        foregroundColor: Colors.orange.shade800,
        elevation: 0,
        actions: [
          // Add refresh button for web platforms (since pull-to-refresh doesn't work on web)
          if (kIsWeb)
            IconButton(
              onPressed: overviewState.isRefreshing ? null : _refreshShoppingLists,
              icon: overviewState.isRefreshing
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.orange.shade800),
                    ),
                  )
                : Icon(Icons.refresh),
              tooltip: overviewState.isRefreshing ? 'جاري التحديث...' : 'تحديث قوائم التسوق',
            ),
        ],
      ),
      backgroundColor: Colors.orange.shade50,
      body: _buildBody(overviewState, isLoading, isGenerating),
      floatingActionButton: (isGenerating || overviewState.isBackgroundGenerating || overviewState.shoppingLists.isEmpty)
          ? null
          : FloatingActionButton.extended(
              onPressed: _generateNewShoppingList,
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
              icon: Icon(Icons.add_shopping_cart),
              label: Text('إنشاء قائمة جديدة'),
            ),
    );
  }

  Widget _buildBody(ShoppingListsState overviewState, bool isLoading, bool isGenerating) {
    // Show error as toast message instead of full screen
    if (overviewState.error != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ShoppingListsErrorToast.show(
          context,
          overviewState.error!,
          () => ref.read(shoppingListsNotifierProvider.notifier).loadShoppingLists(),
        );
        // Clear the error after showing toast
        ref.read(shoppingListsNotifierProvider.notifier).clearError();
      });
    }

    // Show duplicate confirmation dialog
    if (overviewState.pendingGeneration != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showDuplicateConfirmationDialog(overviewState.pendingGeneration!);
      });
    }

    // Wrap everything with RefreshIndicator for mobile pull-to-refresh
    // For web, the refresh button in app bar will trigger the same function
    Widget content;

    if (isLoading) {
      content = const ShoppingListsLoadingWidget();
    } else if (overviewState.shoppingLists.isEmpty) {
      content = ShoppingListsEmptyWidget(
        isLoading: isGenerating || overviewState.isBackgroundGenerating,
        onGeneratePressed: _generateNewShoppingList,
      );
    } else {
      content = _buildShoppingListsContent(overviewState.shoppingLists, isGenerating, overviewState.isBackgroundGenerating);
    }

    // Only wrap with RefreshIndicator on mobile platforms
    if (kIsWeb) {
      return content;
    } else {
      return RefreshIndicator(
        onRefresh: _refreshShoppingLists,
        child: content,
      );
    }
  }



  Widget _buildShoppingListsContent(List<ShoppingList> shoppingLists, bool isGenerating, bool isBackgroundGenerating) {
    return Column(
      children: [
        // Generating indicator
        if (isGenerating || isBackgroundGenerating)
          ShoppingListsGeneratingIndicator(
            message: ref.watch(shoppingListsNotifierProvider).generatingMessage,
          ),

        // Shopping lists
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: _buildListView(shoppingLists),
          ),
        ),
      ],
    );
  }

  Widget _buildListView(List<ShoppingList> shoppingLists) {
    return ListView.builder(
      itemCount: shoppingLists.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: ShoppingListCardWidget(
            shoppingList: shoppingLists[index],
            onTap: () => _navigateToShoppingListDetail(shoppingLists[index]),
            onDelete: () => _showDeleteConfirmationDialog(shoppingLists[index]),
          ),
        );
      },
    );
  }



  void _generateNewShoppingList() {
    ref.read(shoppingListsNotifierProvider.notifier).generateNewShoppingList();
  }

  Future<void> _navigateToShoppingListDetail(ShoppingList shoppingList) async {
    // Navigate to detail page and wait for return
    await context.pushNamed(
      'shopping-list-detail',
      extra: shoppingList,
    );

    // Refresh data when user returns from detail page
    if (mounted) {
      await ref.read(shoppingListsNotifierProvider.notifier).loadShoppingLists();
    }
  }

  void _showDeleteConfirmationDialog(ShoppingList shoppingList) {
    ShoppingListsDialogs.showDeleteConfirmation(context, shoppingList).then((confirmed) {
      if (confirmed == true) {
        _deleteShoppingList(shoppingList);
      }
    });
  }

  Future<void> _deleteShoppingList(ShoppingList shoppingList) async {
    try {
      await ref.read(shoppingListsNotifierProvider.notifier).deleteShoppingList(shoppingList.id);

      if (mounted) {
        ShoppingListsErrorToast.showSuccess(context, 'تم حذف قائمة التسوق بنجاح');
      }
    } catch (e) {
      if (mounted) {
        ShoppingListsErrorToast.showFailure(context, 'فشل في حذف قائمة التسوق: $e');
      }
    }
  }

  void _showDuplicateConfirmationDialog(PendingGeneration pendingGeneration) {
    ShoppingListsDialogs.showDuplicateConfirmation(context, pendingGeneration).then((confirmed) {
      if (confirmed == true) {
        ref.read(shoppingListsNotifierProvider.notifier).confirmGeneration();
      } else {
        ref.read(shoppingListsNotifierProvider.notifier).cancelGeneration();
      }
    });
  }
}
