import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../config/app_config.dart';
import '../utils/logger.dart';
import 'local_storage_service.dart';

part 'firebase_functions_service.g.dart';

/// Service for calling Firebase Functions
class FirebaseFunctionsService {
  late final Dio _dio;
  final LocalStorageService _storageService;

  FirebaseFunctionsService(this._storageService) {
    _dio = Dio();
    _setupDio();
  }

  void _setupDio() {
    _dio.options = BaseOptions(
      baseUrl: AppConfig.functionsBaseUrl,
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60),
      sendTimeout: const Duration(seconds: 60),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // Add auth interceptor
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          try {
            // Get Firebase Auth token
            final user = FirebaseAuth.instance.currentUser;
            if (user != null) {
              final token = await user.getIdToken();
              options.headers['Authorization'] = 'Bearer $token';
            }
          } catch (e) {
            AppLogger.warning('Failed to get auth token: $e');
          }
          handler.next(options);
        },
        onResponse: (response, handler) {
          AppLogger.info('Firebase Functions Response: ${response.statusCode} ${response.requestOptions.path}');
          handler.next(response);
        },
        onError: (error, handler) {
          AppLogger.warning('Firebase Functions Error: ${error.message}');
          handler.next(error);
        },
      ),
    );
  }

  // Generic HTTP Operations

  /// Generic POST request to Firebase Functions
  Future<Map<String, dynamic>> post({
    required String endpoint,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    String? logContext,
  }) async {
    try {
      final context = logContext ?? endpoint;
      AppLogger.info('Firebase Functions POST: $context');

      final response = await _dio.post<Map<String, dynamic>>(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );

      if (response.data == null) {
        throw Exception('No data received from server');
      }

      AppLogger.info('Firebase Functions POST completed: $context');
      return response.data!;
    } on DioException catch (e) {
      AppLogger.warning('DioException in POST $endpoint: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.warning('Exception in POST $endpoint: $e');
      throw Exception('Failed to complete POST request: $e');
    }
  }

  /// Generic GET request to Firebase Functions
  Future<Map<String, dynamic>> get({
    required String endpoint,
    Map<String, dynamic>? queryParameters,
    String? logContext,
  }) async {
    try {
      final context = logContext ?? endpoint;
      AppLogger.info('Firebase Functions GET: $context');

      final response = await _dio.get<Map<String, dynamic>>(
        endpoint,
        queryParameters: queryParameters,
      );

      if (response.data == null) {
        throw Exception('No data received from server');
      }

      AppLogger.info('Firebase Functions GET completed: $context');
      return response.data!;
    } on DioException catch (e) {
      AppLogger.warning('DioException in GET $endpoint: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.warning('Exception in GET $endpoint: $e');
      throw Exception('Failed to complete GET request: $e');
    }
  }

  /// Generic PUT request to Firebase Functions
  Future<Map<String, dynamic>> put({
    required String endpoint,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    String? logContext,
  }) async {
    try {
      final context = logContext ?? endpoint;
      AppLogger.info('Firebase Functions PUT: $context');

      final response = await _dio.put<Map<String, dynamic>>(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );

      if (response.data == null) {
        throw Exception('No data received from server');
      }

      AppLogger.info('Firebase Functions PUT completed: $context');
      return response.data!;
    } on DioException catch (e) {
      AppLogger.warning('DioException in PUT $endpoint: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.warning('Exception in PUT $endpoint: $e');
      throw Exception('Failed to complete PUT request: $e');
    }
  }

  /// Generic DELETE request to Firebase Functions
  Future<Map<String, dynamic>> delete({
    required String endpoint,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    String? logContext,
  }) async {
    try {
      final context = logContext ?? endpoint;
      AppLogger.info('Firebase Functions DELETE: $context');

      final response = await _dio.delete<Map<String, dynamic>>(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );

      if (response.data == null) {
        throw Exception('No data received from server');
      }

      AppLogger.info('Firebase Functions DELETE completed: $context');
      return response.data!;
    } on DioException catch (e) {
      AppLogger.warning('DioException in DELETE $endpoint: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.warning('Exception in DELETE $endpoint: $e');
      throw Exception('Failed to complete DELETE request: $e');
    }
  }

  /// Generic PATCH request to Firebase Functions
  Future<Map<String, dynamic>> patch({
    required String endpoint,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    String? logContext,
  }) async {
    try {
      final context = logContext ?? endpoint;
      AppLogger.info('Firebase Functions PATCH: $context');

      final response = await _dio.patch<Map<String, dynamic>>(
        endpoint,
        data: data,
        queryParameters: queryParameters,
      );

      if (response.data == null) {
        throw Exception('No data received from server');
      }

      AppLogger.info('Firebase Functions PATCH completed: $context');
      return response.data!;
    } on DioException catch (e) {
      AppLogger.warning('DioException in PATCH $endpoint: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.warning('Exception in PATCH $endpoint: $e');
      throw Exception('Failed to complete PATCH request: $e');
    }
  }

  /// Get current authenticated user ID
  String? getCurrentUserId() {
    return FirebaseAuth.instance.currentUser?.uid;
  }

  /// Check if user is authenticated
  bool isAuthenticated() {
    return FirebaseAuth.instance.currentUser != null;
  }

  // Legacy Methods (Deprecated - use generic methods instead)

  @Deprecated('Use post() with endpoint: "/meal-plans/generate"')
  Future<Map<String, dynamic>> generateMealPlan() async {
    return post(
      endpoint: '/meal-plans/generate',
      data: {},
      logContext: 'Generating meal plan',
    );
  }

  @Deprecated('Use post() with endpoint: "/analyze-nutrition"')
  Future<Map<String, dynamic>> analyzeNutrition({
    required List<String> foodItems,
    required String portion,
  }) async {
    return post(
      endpoint: '/analyze-nutrition',
      data: {
        'foodItems': foodItems,
        'portion': portion,
      },
      logContext: 'Analyzing nutrition for: $foodItems',
    );
  }

  @Deprecated('Use get() with endpoint: "/meal-plans/{userId}"')
  Future<Map<String, dynamic>> getMealPlans({
    int limit = 10,
    String status = 'active',
  }) async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    return get(
      endpoint: '/meal-plans/${user.uid}',
      queryParameters: {
        'limit': limit,
        'status': status,
      },
      logContext: 'Fetching meal plans for user: ${user.uid}',
    );
  }

  @Deprecated('Use post() with endpoint: "/meal-plans/replace-meal"')
  Future<Map<String, dynamic>> replaceMeal({
    required String dayDocumentId,
    required String mealId,
    String? customIngredients,
  }) async {
    return post(
      endpoint: '/meal-plans/replace-meal',
      data: {
        'day_document_id': dayDocumentId,  // Use snake_case as expected by Firebase function
        'meal_id': mealId,                 // Use snake_case as expected by Firebase function
        if (customIngredients != null) 'custom_ingredients': customIngredients,  // Use snake_case
      },
      logContext: 'Requesting meal replacement for meal ID $mealId in day document $dayDocumentId',
    );
  }

  @Deprecated('Use post() with endpoint: "/shopping-list/generate-from-meals"')
  Future<Map<String, dynamic>> generateShoppingListFromMeals({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return post(
      endpoint: '/shopping-list/generate-from-meals',
      data: {
        'start_date': startDate.toIso8601String().split('T')[0],
        'end_date': endDate.toIso8601String().split('T')[0],
      },
      logContext: 'Requesting shopping list generation from ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}',
    );
  }

  Exception _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception('Connection timeout. Please check your internet connection.');

      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['error'] ?? e.message;

        switch (statusCode) {
          case 400:
            return Exception('Invalid request: $message');
          case 401:
            return Exception('Authentication failed. Please sign in again.');
          case 403:
            return Exception('Access denied: $message');
          case 404:
            return Exception('Service not found: $message');
          case 500:
            return Exception('Server error: $message');
          default:
            return Exception('Request failed ($statusCode): $message');
        }

      case DioExceptionType.cancel:
        return Exception('Request was cancelled');

      case DioExceptionType.connectionError:
        return Exception('No internet connection');

      default:
        return Exception('Network error: ${e.message}');
    }
  }

  void dispose() {
    _dio.close();
  }
}

@riverpod
Future<FirebaseFunctionsService> firebaseFunctionsService(FirebaseFunctionsServiceRef ref) async {
  final storageService = await ref.watch(localStorageServiceProvider.future);
  return FirebaseFunctionsService(storageService);
}
