import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/features/shopping_list/providers/shopping_lists_provider.dart';
import 'package:easydietai/features/shopping_list/providers/shopping_list_generation_provider.dart';
import 'package:easydietai/features/shopping_list/data/models/shopping_list_models.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_lists_loading_widget.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_lists_empty_widget.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_lists_generating_indicator.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_list_card_widget.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_lists_error_toast.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/shopping_lists_dialogs.dart';
import 'package:easydietai/features/shopping_list/presentation/widgets/generate_shopping_list_card.dart';

class ShoppingListsPage extends ConsumerStatefulWidget {
  const ShoppingListsPage({super.key});

  @override
  ConsumerState<ShoppingListsPage> createState() => _ShoppingListsPageState();
}

class _ShoppingListsPageState extends ConsumerState<ShoppingListsPage> {
  bool _isGeneratingShoppingList = false;
  bool _isBackgroundLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Check background loading state
      _checkBackgroundLoading();
      // Initialize loading state through provider
      ref.read(shoppingListsNotifierProvider.notifier).initializeLoadingState();
      // Load shopping lists
      ref.read(shoppingListsNotifierProvider.notifier).loadShoppingLists();
    });
  }

  /// Check if shopping list generation is currently running in background
  Future<void> _checkBackgroundLoading() async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      final isLoading = localStorageService.isGeneratingShoppingList();

      if (mounted) {
        setState(() {
          _isBackgroundLoading = isLoading;
        });
      }
    } catch (e) {
      // Ignore errors in checking background loading
    }
  }



  /// Refresh shopping lists from Firestore and update local storage
  Future<void> _refreshShoppingLists() async {
    await ref.read(shoppingListsNotifierProvider.notifier).refreshShoppingLists();
  }

  @override
  Widget build(BuildContext context) {
    final overviewState = ref.watch(shoppingListsNotifierProvider);
    final isLoading = ref.watch(isShoppingListsLoadingProvider);
    final isGenerating = ref.watch(isShoppingListGeneratingProvider);
    final shoppingListGenerationState = ref.watch(shoppingListGenerationNotifierProvider);

    // Check for errors from both providers
    final hasShoppingListGenerationError = shoppingListGenerationState.hasError;
    final hasOverviewError = overviewState.error != null;
    final hasError = hasShoppingListGenerationError || hasOverviewError;

    // Check if shopping list generation is loading (for button-level loading)
    final isShoppingListGenerationLoading = shoppingListGenerationState.isLoading;

    return Scaffold(
      appBar: AppBar(
        title: Text('قوائم التسوق'),
        backgroundColor: Colors.orange.shade50,
        foregroundColor: Colors.orange.shade800,
        elevation: 0,
        actions: [
          // Add refresh button for web platforms (since pull-to-refresh doesn't work on web)
          if (kIsWeb)
            IconButton(
              onPressed: overviewState.isRefreshing ? null : _refreshShoppingLists,
              icon: overviewState.isRefreshing
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.orange.shade800),
                    ),
                  )
                : Icon(Icons.refresh),
              tooltip: overviewState.isRefreshing ? 'جاري التحديث...' : 'تحديث قوائم التسوق',
            ),
        ],
      ),
      backgroundColor: Colors.orange.shade50,
      body: _buildBody(overviewState, isLoading, isGenerating, isShoppingListGenerationLoading),
      floatingActionButton: (_isGeneratingShoppingList || _isBackgroundLoading || isGenerating || overviewState.isBackgroundGenerating || isShoppingListGenerationLoading || overviewState.shoppingLists.isEmpty)
          ? null
          : FloatingActionButton.extended(
              onPressed: hasError ? _handleRetryFromFAB : _generateNewShoppingList,
              backgroundColor: hasError ? Colors.red.shade600 : Colors.orange.shade600,
              foregroundColor: Colors.white,
              icon: Icon(hasError ? Icons.refresh : Icons.add_shopping_cart),
              label: Text(hasError ? 'إعادة المحاولة' : 'إنشاء قائمة جديدة'),
            ),
    );
  }

  Widget _buildBody(ShoppingListsState overviewState, bool isLoading, bool isGenerating, bool isShoppingListGenerationLoading) {
    // Show error as toast message instead of full screen
    if (overviewState.error != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ShoppingListsErrorToast.show(
          context,
          overviewState.error!,
          () => ref.read(shoppingListsNotifierProvider.notifier).loadShoppingLists(),
        );
        // Clear the error after showing toast
        ref.read(shoppingListsNotifierProvider.notifier).clearError();
      });
    }

    // Show duplicate confirmation dialog
    if (overviewState.pendingGeneration != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showDuplicateConfirmationDialog(overviewState.pendingGeneration!);
      });
    }

    // Wrap everything with RefreshIndicator for mobile pull-to-refresh
    // For web, the refresh button in app bar will trigger the same function
    Widget content;

    if (isLoading) {
      content = const ShoppingListsLoadingWidget();
    } else if (overviewState.shoppingLists.isEmpty) {
      content = ShoppingListsEmptyWidget(
        isLoading: isGenerating || overviewState.isBackgroundGenerating,
        onGeneratePressed: _generateNewShoppingList,
      );
    } else {
      content = _buildShoppingListsContent(overviewState.shoppingLists, isGenerating, overviewState.isBackgroundGenerating);
    }

    // Only wrap with RefreshIndicator on mobile platforms
    if (kIsWeb) {
      return content;
    } else {
      return RefreshIndicator(
        onRefresh: _refreshShoppingLists,
        child: content,
      );
    }
  }



  Widget _buildShoppingListsContent(List<ShoppingList> shoppingLists, bool isGenerating, bool isBackgroundGenerating) {
    return Column(
      children: [
        // Generating indicator
        if (isGenerating || isBackgroundGenerating)
          ShoppingListsGeneratingIndicator(
            message: ref.watch(shoppingListsNotifierProvider).generatingMessage,
          ),

        // Shopping lists
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: _buildListView(shoppingLists),
          ),
        ),
      ],
    );
  }

  Widget _buildListView(List<ShoppingList> shoppingLists) {
    return ListView.builder(
      itemCount: shoppingLists.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: ShoppingListCardWidget(
            shoppingList: shoppingLists[index],
            onTap: () => _navigateToShoppingListDetail(shoppingLists[index]),
            onDelete: () => _showDeleteConfirmationDialog(shoppingLists[index]),
          ),
        );
      },
    );
  }



  void _generateNewShoppingList() {
    ref.read(shoppingListsNotifierProvider.notifier).generateNewShoppingList();
  }

  Future<void> _navigateToShoppingListDetail(ShoppingList shoppingList) async {
    // Navigate to detail page and wait for return
    await context.pushNamed(
      'shopping-list-detail',
      extra: shoppingList,
    );

    // Refresh data when user returns from detail page
    if (mounted) {
      await ref.read(shoppingListsNotifierProvider.notifier).loadShoppingLists();
    }
  }

  void _showDeleteConfirmationDialog(ShoppingList shoppingList) {
    ShoppingListsDialogs.showDeleteConfirmation(context, shoppingList).then((confirmed) {
      if (confirmed == true) {
        _deleteShoppingList(shoppingList);
      }
    });
  }

  Future<void> _deleteShoppingList(ShoppingList shoppingList) async {
    try {
      await ref.read(shoppingListsNotifierProvider.notifier).deleteShoppingList(shoppingList.id);

      if (mounted) {
        ShoppingListsErrorToast.showSuccess(context, 'تم حذف قائمة التسوق بنجاح');
      }
    } catch (e) {
      if (mounted) {
        ShoppingListsErrorToast.showFailure(context, 'فشل في حذف قائمة التسوق: $e');
      }
    }
  }

  void _showDuplicateConfirmationDialog(PendingGeneration pendingGeneration) {
    ShoppingListsDialogs.showDuplicateConfirmation(context, pendingGeneration).then((confirmed) {
      if (confirmed == true) {
        ref.read(shoppingListsNotifierProvider.notifier).confirmGeneration();
      } else {
        ref.read(shoppingListsNotifierProvider.notifier).cancelGeneration();
      }
    });
  }
}
