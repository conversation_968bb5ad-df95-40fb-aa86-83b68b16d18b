import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';


import '../../../../core/utils/logger.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../providers/meal_plan_generation_provider.dart';
import '../../providers/current_meal_plan_provider.dart';
import '../../providers/meal_days_generation_provider.dart';
import '../widgets/meal_plan_loading_view.dart';
import '../widgets/meal_plan_card_view.dart';
import '../../data/models/meal_plan_request.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  @override
  void initState() {
    super.initState();
    // Load user preferences and meal plan from storage when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializePage();
    });
  }

  /// Initialize the page by loading user preferences and meal plan from storage
  Future<void> _initializePage() async {
    final notifier = ref.read(mealPlanGenerationNotifierProvider.notifier);

    // Load user preferences first
    notifier.loadUserPreferences();

    // Then try to load existing meal plan from local storage
    await notifier.loadMealPlanFromStorage();

    // Only sync from Firestore if no local data exists
    // This prioritizes local storage and reduces unnecessary Firestore calls
    await _syncMealPlanIfNeeded();

    // Initialize the meal days generation provider (this will check local state and start listeners)
    ref.read(mealDaysGenerationNotifierProvider);
  }

  /// Sync meal plan data from Firestore only if no local data exists
  /// This optimizes loading by prioritizing local storage
  Future<void> _syncMealPlanIfNeeded() async {
    try {
      // Check if we have local meal plan data
      final currentMealPlanNotifier = ref.read(currentMealPlanNotifierProvider.notifier);
      final hasLocalData = await currentMealPlanNotifier.hasLocalMealPlanData();

      if (!hasLocalData) {
        AppLogger.info('HomePage: No local meal plan data found, syncing from Firestore');
        await _syncMealPlanFromFirestore();
      } else {
        AppLogger.info('HomePage: Local meal plan data exists, skipping automatic Firestore sync');
      }
    } catch (e) {
      AppLogger.warning('HomePage: Failed to check local meal plan data: $e');
      // If we can't check local data, fall back to syncing from Firestore
      await _syncMealPlanFromFirestore();
    }
  }

  /// Sync meal plan data from Firestore to local storage
  /// This is called when the home page initializes to ensure we have the latest data
  Future<void> _syncMealPlanFromFirestore() async {
    try {
      AppLogger.info('HomePage: Syncing meal plan data from Firestore');

      // Use the current meal plan provider's sync method to ensure proper state management
      final currentMealPlanNotifier = ref.read(currentMealPlanNotifierProvider.notifier);
      final mealPlanSyncResult = await currentMealPlanNotifier.syncFromFirestore();

      if (mealPlanSyncResult) {
        AppLogger.info('HomePage: Meal plan data synced successfully from Firestore');
      } else {
        AppLogger.info('HomePage: No meal plan data found in Firestore or sync failed');
      }
    } catch (e) {
      AppLogger.warning('HomePage: Failed to sync meal plan data from Firestore: $e');
      // Don't throw error as this shouldn't break the home page loading
    }
  }

  /// Handle pull-to-refresh gesture
  /// Forces fresh data fetch from Firestore and updates local storage
  Future<void> _handleRefresh() async {
    try {
      AppLogger.info('HomePage: Pull-to-refresh triggered, forcing Firestore sync');

      // Set refreshing state
      final currentMealPlanNotifier = ref.read(currentMealPlanNotifierProvider.notifier);
      currentMealPlanNotifier.setRefreshing(true);

      // Force sync from Firestore regardless of local data
      final mealPlanSyncResult = await currentMealPlanNotifier.syncFromFirestore();

      if (mealPlanSyncResult) {
        AppLogger.info('HomePage: Pull-to-refresh completed successfully');
      } else {
        AppLogger.warning('HomePage: Pull-to-refresh failed to sync data');
      }
    } catch (e) {
      AppLogger.warning('HomePage: Pull-to-refresh failed: $e');
    } finally {
      // Clear refreshing state
      final currentMealPlanNotifier = ref.read(currentMealPlanNotifierProvider.notifier);
      currentMealPlanNotifier.setRefreshing(false);

      // Also refresh the days generation state
      final daysGenerationNotifier = ref.read(mealDaysGenerationNotifierProvider.notifier);
      await daysGenerationNotifier.refreshState();
    }
  }

  /// Check if background loading should be shown
  /// Only shows global loading during onboarding when no meal plan exists
  /// Extra days generation should show loading at button level, not globally
  bool _isBackgroundLoading() {
    try {
      final localStorageService = ref.read(localStorageServiceProvider);
      return localStorageService.when(
        data: (service) {
          // Check if generating flag exists and is valid
          final isGeneratingFlagValid = service.isMealGenerationLoading();

          // Check if current meal plan exists in local storage
          final currentMealPlanData = service.loadCurrentMealPlan();
          final hasMealPlan = currentMealPlanData != null;

          // Only show global background loading during onboarding if:
          // 1. Generating flag exists and is valid
          // 2. AND no current meal plan exists (empty local storage)
          // This ensures extra days generation shows button-level loading, not global loading
          return isGeneratingFlagValid && !hasMealPlan;
        },
        loading: () => false,
        error: (error, stack) => false,
      );
    } catch (e) {
      AppLogger.warning('HomePage: Failed to check background loading state: $e');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final mealPlanState = ref.watch(mealPlanGenerationNotifierProvider);
    final currentMealPlanState = ref.watch(currentMealPlanNotifierProvider);

    // Use current meal plan if available, otherwise fall back to generated plan
    final activePlan = currentMealPlanState.currentPlan ?? mealPlanState.generatedPlan;

    // Check if background loading should be shown (only when no meal plan exists and generation flag is valid)
    final showBackgroundLoading = _isBackgroundLoading();

    return Scaffold(
      body: showBackgroundLoading
          ? const MealPlanLoadingView()
          : MealPlanCardView(
              plan: activePlan,
              onMealTap: _navigateToMealDetails,
              includeAppBar: true, // Tell MealPlanCardView to include the SliverAppBar
              onRefresh: _handleRefresh, // Add pull-to-refresh functionality
            ),
    );
  }



  void _navigateToMealDetails(GeneratedMeal meal, DayMealPlan dayMealPlan) {
    context.pushNamed(
      'meal-details',
      extra: {
        'meal': meal,
        'dayMealPlan': dayMealPlan,
      },
    );
  }
}
