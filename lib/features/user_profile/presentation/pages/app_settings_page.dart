import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/logger.dart';
import '../../../../shared/providers/app_state_provider.dart';

class AppSettingsPage extends ConsumerWidget {
  const AppSettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLanguage = ref.watch(currentLanguageProvider);
    final currentTheme = ref.watch(currentThemeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات التطبيق'),
        backgroundColor: AppColors.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Language & Theme Section
            _buildSectionCard(
              context,
              'اللغة والمظهر',
              [
                _buildSettingItem(
                  context,
                  'اللغة',
                  _getLanguageDisplayName(currentLanguage),
                  Icons.language,
                  () => _showLanguageDialog(context, ref, currentLanguage),
                ),
                _buildSettingItem(
                  context,
                  'المظهر',
                  _getThemeDisplayName(currentTheme),
                  Icons.palette,
                  () => _showThemeDialog(context, ref, currentTheme),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Help & Support Section
            _buildSectionCard(
              context,
              'المساعدة والدعم',
              [
                _buildSettingItem(
                  context,
                  'الأسئلة الشائعة',
                  'احصل على إجابات للأسئلة الشائعة',
                  Icons.help_outline,
                  () => context.push('/profile/faq'),
                ),
                _buildSettingItem(
                  context,
                  'المساعدة والدعم',
                  'تواصل معنا للحصول على المساعدة',
                  Icons.support_agent,
                  () => context.push('/profile/help-support'),
                ),
                _buildSettingItem(
                  context,
                  'حول التطبيق',
                  'معلومات عن التطبيق والفريق',
                  Icons.info,
                  () => context.push('/profile/about'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Danger Zone Section
            _buildSectionCard(
              context,
              'المنطقة الخطرة',
              [
                _buildDangerItem(
                  context,
                  'حذف الحساب',
                  'حذف الحساب نهائياً مع جميع البيانات',
                  Icons.delete_forever,
                  () => _showDeleteAccountDialog(context, ref),
                ),
              ],
              isDangerZone: true,
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(
    BuildContext context,
    String title,
    List<Widget> children, {
    bool isDangerZone = false,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: isDangerZone ? AppColors.error.withOpacity(0.05) : AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDangerZone ? AppColors.error.withOpacity(0.2) : AppColors.border,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: isDangerZone ? AppColors.error : AppColors.textPrimary,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.primary,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge,
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppColors.textSecondary,
      ),
      onTap: onTap,
    );
  }

  Widget _buildDangerItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.error,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: AppColors.error,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppColors.error.withOpacity(0.7),
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppColors.error.withOpacity(0.7),
      ),
      onTap: onTap,
    );
  }

  String _getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return 'العربية';
      case 'en':
        return 'English';
      default:
        return 'العربية';
    }
  }

  String _getThemeDisplayName(String theme) {
    switch (theme) {
      case 'light':
        return 'فاتح';
      case 'dark':
        return 'داكن';
      case 'system':
        return 'تلقائي (حسب النظام)';
      default:
        return 'تلقائي (حسب النظام)';
    }
  }

  void _showLanguageDialog(BuildContext context, WidgetRef ref, String currentLanguage) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر اللغة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'ar',
              groupValue: currentLanguage,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appStateNotifierProvider.notifier).changeLanguage(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: currentLanguage,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appStateNotifierProvider.notifier).changeLanguage(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showThemeDialog(BuildContext context, WidgetRef ref, String currentTheme) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر المظهر'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('فاتح'),
              value: 'light',
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appStateNotifierProvider.notifier).changeTheme(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('داكن'),
              value: 'dark',
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appStateNotifierProvider.notifier).changeTheme(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('تلقائي (حسب النظام)'),
              value: 'system',
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(appStateNotifierProvider.notifier).changeTheme(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الحساب'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تحذير: هذا الإجراء لا يمكن التراجع عنه!',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.error,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'سيتم حذف جميع بياناتك نهائياً بما في ذلك:\n'
              '• خطط الوجبات\n'
              '• تتبع الوزن\n'
              '• قوائم التسوق\n'
              '• إعدادات الحساب',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _handleDeleteAccount(context, ref);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف الحساب'),
          ),
        ],
      ),
    );
  }

  Future<void> _handleDeleteAccount(BuildContext context, WidgetRef ref) async {
    try {
      AppLogger.info('AppSettingsPage: Starting account deletion process');
      
      // Show loading indicator
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('جاري حذف الحساب...'),
            duration: Duration(seconds: 2),
          ),
        );
      }

      // TODO: Implement account deletion logic
      // This would typically involve:
      // 1. Calling a Firebase function to delete user data
      // 2. Deleting the Firebase Auth user
      // 3. Clearing local storage
      // 4. Navigating to auth page

      AppLogger.info('AppSettingsPage: Account deletion completed');
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الحساب بنجاح'),
            backgroundColor: AppColors.success,
            duration: Duration(seconds: 2),
          ),
        );
        
        // Navigate to auth page
        context.go('/auth');
      }
    } catch (e) {
      AppLogger.error('AppSettingsPage: Error deleting account: $e');
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء حذف الحساب'),
            backgroundColor: AppColors.error,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
