import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/services/local_storage_service.dart';
import '../../../../core/services/firestore_service.dart';

part 'meal_days_generation_service.g.dart';

/// Service for managing meal days generation with background job tracking
/// Follows the same pattern as meal_replacement_service for consistency
class MealDaysGenerationService {
  final LocalStorageService _localStorageService;
  final FirestoreService _firestoreService;

  MealDaysGenerationService({
    required LocalStorageService localStorageService,
    required FirestoreService firestoreService,
  })  : _localStorageService = localStorageService,
        _firestoreService = firestoreService;

  /// Create a Firestore listener for meal days generation using bjt_single pattern
  /// Monitors /users/{userId}/bjt_single/days_generating document
  StreamSubscription<DocumentSnapshot>? createDaysGenerationListener({
    required void Function(String status, Map<String, dynamic> data) onStatusChange,
    required void Function(dynamic error) onError,
  }) {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      debugPrint('MealDaysGenerationService: No authenticated user for listener');
      return null;
    }

    try {
      debugPrint('MealDaysGenerationService: Creating Firestore listener for days generation');

      return FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('bjt_single')
          .doc('bjt_days_generating')
          .snapshots()
          .listen(
        (snapshot) {
          if (!snapshot.exists) {
            debugPrint('MealDaysGenerationService: bjt_single document does not exist');
            return;
          }

          final data = snapshot.data() as Map<String, dynamic>;
          final status = data['status'] as String? ?? 'unknown';

          debugPrint('MealDaysGenerationService: bjt_single status: $status');
          onStatusChange(status, data);
        },
        onError: (error) {
          debugPrint('MealDaysGenerationService: Listener error: $error');
          onError(error);
        },
      );
    } catch (e) {
      debugPrint('MealDaysGenerationService: Error creating listener: $e');
      onError(e);
      return null;
    }
  }

  /// Check if there's a local loading flag for days generation
  bool hasLocalLoadingFlag() {
    try {
      return _localStorageService.isMealDaysGenerationLoading();
    } catch (e) {
      debugPrint('MealDaysGenerationService: Error checking local loading flag: $e');
      return false;
    }
  }

  /// Set local loading flag for days generation
  Future<bool> setLocalLoadingFlag() async {
    try {
      return await _localStorageService.saveMealDaysGenerationLoading(true);
    } catch (e) {
      debugPrint('MealDaysGenerationService: Error setting local loading flag: $e');
      return false;
    }
  }

  /// Clear local loading flag for days generation
  Future<bool> clearLocalLoadingFlag() async {
    try {
      return await _localStorageService.clearMealDaysGenerationLoading();
    } catch (e) {
      debugPrint('MealDaysGenerationService: Error clearing local loading flag: $e');
      return false;
    }
  }

  /// Get local error data for days generation
  Map<String, dynamic>? getLocalErrorData() {
    try {
      return _localStorageService.loadMealDaysGenerationError();
    } catch (e) {
      debugPrint('MealDaysGenerationService: Error getting local error data: $e');
      return null;
    }
  }

  /// Save local error data for days generation
  Future<bool> saveLocalErrorData(String errorMessage) async {
    try {
      return await _localStorageService.saveMealDaysGenerationError(errorMessage);
    } catch (e) {
      debugPrint('MealDaysGenerationService: Error saving local error data: $e');
      return false;
    }
  }

  /// Clear local error data for days generation
  Future<bool> clearLocalErrorData() async {
    try {
      return await _localStorageService.clearMealDaysGenerationError();
    } catch (e) {
      debugPrint('MealDaysGenerationService: Error clearing local error data: $e');
      return false;
    }
  }

  /// Get the latest day datetime from local storage
  /// Returns null if no days exist in local storage
  DateTime? getLatestDayDatetime() {
    try {
      final mealPlanData = _localStorageService.loadCurrentMealPlan();
      if (mealPlanData == null) return null;

      final days = mealPlanData['days'] as List<dynamic>?;
      if (days == null || days.isEmpty) return null;

      // Find the latest date among all days
      DateTime? latestDate;
      for (final dayData in days) {
        final dateStr = dayData['date'] as String?;
        if (dateStr != null) {
          final date = DateTime.parse(dateStr);
          if (latestDate == null || date.isAfter(latestDate)) {
            latestDate = date;
          }
        }
      }

      return latestDate;
    } catch (e) {
      debugPrint('MealDaysGenerationService: Error getting latest day datetime: $e');
      return null;
    }
  }

  /// Sync new days from Firestore to local storage
  /// Gets all days with date after the specified cutoff date
  Future<bool> syncNewDaysFromFirestore(DateTime cutoffDate) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        debugPrint('MealDaysGenerationService: No authenticated user for sync');
        return false;
      }

      debugPrint('MealDaysGenerationService: Syncing new days from Firestore after: ${cutoffDate.toIso8601String()}');

      // Query for days with date > cutoffDate
      final querySnapshot = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('days')
          .where('date', isGreaterThan: Timestamp.fromDate(cutoffDate))
          .orderBy('date', descending: false)
          .get();

      if (querySnapshot.docs.isEmpty) {
        debugPrint('MealDaysGenerationService: No new days found to sync');
        return true;
      }

      debugPrint('MealDaysGenerationService: Found ${querySnapshot.docs.length} new days to sync');

      // Convert Firestore documents to day data
      final newDays = querySnapshot.docs.map((doc) {
        final data = doc.data();
        final convertedData = _convertTimestampsToStrings(data);
        return {
          ...convertedData,
          'day_document_id': doc.id,
          'date': (data['date'] as Timestamp).toDate().toIso8601String(),
        };
      }).toList();

      // Merge with existing local data
      return await _mergeNewDaysWithLocalStorage(newDays);
    } catch (e) {
      debugPrint('MealDaysGenerationService: Error syncing new days: $e');
      return false;
    }
  }

  /// Merge new days with existing local storage data
  Future<bool> _mergeNewDaysWithLocalStorage(List<Map<String, dynamic>> newDays) async {
    try {
      // Load existing meal plan data
      final existingData = _localStorageService.loadCurrentMealPlan();
      
      if (existingData == null) {
        // No existing data, create new meal plan with the new days
        final newMealPlan = {
          'days': newDays,
          'total_nutrition': _calculateTotalNutrition(newDays),
          'created_at': DateTime.now().toIso8601String(),
          'last_updated': DateTime.now().toIso8601String(),
        };
        
        return await _localStorageService.saveCurrentMealPlan(newMealPlan);
      }

      // Merge with existing data
      final existingDays = List<Map<String, dynamic>>.from(existingData['days'] as List<dynamic>? ?? []);
      
      // Add new days to existing days
      existingDays.addAll(newDays);
      
      // Sort days by date
      existingDays.sort((a, b) {
        final dateA = DateTime.parse(a['date'] as String);
        final dateB = DateTime.parse(b['date'] as String);
        return dateA.compareTo(dateB);
      });

      // Update meal plan data (convert any remaining timestamps)
      final cleanExistingData = _convertTimestampsToStrings(existingData);
      final updatedMealPlan = {
        ...cleanExistingData,
        'days': existingDays,
        'total_nutrition': _calculateTotalNutrition(existingDays),
        'last_updated': DateTime.now().toIso8601String(),
      };

      return await _localStorageService.saveCurrentMealPlan(updatedMealPlan);
    } catch (e) {
      debugPrint('MealDaysGenerationService: Error merging new days: $e');
      return false;
    }
  }

  /// Convert Firestore Timestamp objects to ISO8601 strings recursively
  /// This ensures data can be serialized to JSON for local storage
  Map<String, dynamic> _convertTimestampsToStrings(Map<String, dynamic> data) {
    final Map<String, dynamic> converted = {};

    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is Timestamp) {
        // Convert Timestamp to ISO8601 string
        converted[key] = value.toDate().toIso8601String();
      } else if (value is Map<String, dynamic>) {
        // Recursively convert nested maps
        converted[key] = _convertTimestampsToStrings(value);
      } else if (value is List) {
        // Convert lists (might contain maps with timestamps)
        converted[key] = _convertTimestampsInList(value);
      } else {
        // Keep other values as-is
        converted[key] = value;
      }
    }

    return converted;
  }

  /// Convert Firestore Timestamps in lists recursively
  List<dynamic> _convertTimestampsInList(List<dynamic> list) {
    return list.map((item) {
      if (item is Timestamp) {
        return item.toDate().toIso8601String();
      } else if (item is Map<String, dynamic>) {
        return _convertTimestampsToStrings(item);
      } else if (item is List) {
        return _convertTimestampsInList(item);
      } else {
        return item;
      }
    }).toList();
  }

  /// Calculate total nutrition for all days
  Map<String, dynamic> _calculateTotalNutrition(List<Map<String, dynamic>> days) {
    double totalCalories = 0;
    double totalProtein = 0;
    double totalCarbs = 0;
    double totalFat = 0;

    for (final day in days) {
      final meals = day['meals'] as List<dynamic>? ?? [];
      for (final meal in meals) {
        final nutrition = meal['nutrition'] as Map<String, dynamic>? ?? {};
        totalCalories += (nutrition['calories'] as num?)?.toDouble() ?? 0;
        totalProtein += (nutrition['protein'] as num?)?.toDouble() ?? 0;
        totalCarbs += (nutrition['carbs'] as num?)?.toDouble() ?? 0;
        totalFat += (nutrition['fat'] as num?)?.toDouble() ?? 0;
      }
    }

    return {
      'calories': totalCalories,
      'protein': totalProtein,
      'carbs': totalCarbs,
      'fat': totalFat,
    };
  }

  /// Get the current date for focusing on today's meals
  DateTime getCurrentDate() {
    return DateTime.now();
  }

  /// Find the best day to focus on based on current date
  /// Returns the day that matches today, or the next day after today, or null if none found
  Map<String, dynamic>? findBestDayToFocus() {
    try {
      final mealPlanData = _localStorageService.loadCurrentMealPlan();
      if (mealPlanData == null) return null;

      final days = mealPlanData['days'] as List<dynamic>?;
      if (days == null || days.isEmpty) return null;

      final today = DateTime.now();
      final todayDateOnly = DateTime(today.year, today.month, today.day);

      // First, try to find today's date
      for (final dayData in days) {
        final dateStr = dayData['date'] as String?;
        if (dateStr != null) {
          final date = DateTime.parse(dateStr);
          final dayDateOnly = DateTime(date.year, date.month, date.day);
          
          if (dayDateOnly.isAtSameMomentAs(todayDateOnly)) {
            return dayData as Map<String, dynamic>;
          }
        }
      }

      // If today's date not found, find the next day after today
      Map<String, dynamic>? nextDay;
      for (final dayData in days) {
        final dateStr = dayData['date'] as String?;
        if (dateStr != null) {
          final date = DateTime.parse(dateStr);
          final dayDateOnly = DateTime(date.year, date.month, date.day);
          
          if (dayDateOnly.isAfter(todayDateOnly)) {
            if (nextDay == null) {
              nextDay = dayData as Map<String, dynamic>;
            } else {
              final nextDayDate = DateTime.parse(nextDay['date'] as String);
              if (date.isBefore(nextDayDate)) {
                nextDay = dayData as Map<String, dynamic>;
              }
            }
          }
        }
      }

      return nextDay;
    } catch (e) {
      debugPrint('MealDaysGenerationService: Error finding best day to focus: $e');
      return null;
    }
  }
}

@riverpod
Future<MealDaysGenerationService> mealDaysGenerationService(MealDaysGenerationServiceRef ref) async {
  final localStorageService = await ref.watch(localStorageServiceProvider.future);
  final firestoreService = ref.watch(firestoreServiceProvider);

  return MealDaysGenerationService(
    localStorageService: localStorageService,
    firestoreService: firestoreService,
  );
}
