const {
  createNotification,
  getNotifications,
  markAllNotificationsAsSeen,
  getNotificationMetadata,
  NOTIFICATION_TYPES,
} = require('../services/notificationService');

// Mock Firebase Admin
jest.mock('../config/firebase', () => ({
  initializeFirebase: jest.fn(() => ({
    firestore: jest.fn(() => ({
      collection: jest.fn(() => ({
        doc: jest.fn(() => ({
          get: jest.fn(),
          set: jest.fn(),
          update: jest.fn(),
          collection: jest.fn(() => ({
            doc: jest.fn(() => ({
              set: jest.fn(),
              get: jest.fn(),
              collection: jest.fn(() => ({
                doc: jest.fn(() => ({
                  set: jest.fn(),
                  get: jest.fn(),
                })),
                orderBy: jest.fn(() => ({
                  limit: jest.fn(() => ({
                    get: jest.fn(),
                  })),
                })),
                where: jest.fn(() => ({
                  get: jest.fn(),
                })),
              })),
            })),
          })),
        })),
      })),
      batch: jest.fn(() => ({
        set: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
        commit: jest.fn(),
      })),
    })),
  })),
}));

describe('NotificationService - New Structure', () => {
  const mockUserId = 'test-user-123';
  const mockNotificationData = {
    type: NOTIFICATION_TYPES.MEAL_PLAN_READY,
    content: {
      duration: 7,
      planType: 'standard',
    },
    action: {
      type: 'internalNavigation',
      route: '/home',
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createNotification', () => {
    it('should create notification in new structure', async () => {
      const mockFirestore = require('../config/firebase').initializeFirebase().firestore();
      const mockUserDoc = {
        exists: true,
        data: () => ({ preferred_language: 'en' }),
      };
      const mockMetaDoc = {
        exists: true,
        data: () => ({ total_count: 5, unseen_count: 2 }),
      };

      // Mock user document get
      mockFirestore.collection().doc().get.mockResolvedValueOnce(mockUserDoc);
      
      // Mock meta document get for updateNotificationMetadata
      mockFirestore.collection().doc().collection().doc().get.mockResolvedValueOnce(mockMetaDoc);
      
      // Mock notification creation
      mockFirestore.collection().doc().collection().doc().collection().doc().set.mockResolvedValueOnce();
      
      // Mock meta document update
      mockFirestore.collection().doc().collection().doc().set.mockResolvedValueOnce();

      const result = await createNotification(mockUserId, mockNotificationData);

      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('title');
      expect(result).toHaveProperty('message');
      expect(result).toHaveProperty('timestamp');
      expect(result.seen).toBe(false);
      expect(result.type).toBe(NOTIFICATION_TYPES.MEAL_PLAN_READY);
    });
  });

  describe('Notification Structure', () => {
    it('should follow the correct collection path structure', () => {
      // This test verifies that our service follows the expected structure:
      // users/{userId}/notifications/meta (document with total_count, unseen_count)
      // users/{userId}/notifications/history/items/{notificationId} (notification documents)
      
      const expectedStructure = {
        metaPath: 'users/{userId}/notifications/meta',
        historyPath: 'users/{userId}/notifications/history/items/{notificationId}',
      };

      expect(expectedStructure.metaPath).toContain('notifications/meta');
      expect(expectedStructure.historyPath).toContain('notifications/history/items');
    });
  });
});

describe('Notification Types', () => {
  it('should have all required notification types', () => {
    expect(NOTIFICATION_TYPES).toHaveProperty('SHOPPING_LIST_READY');
    expect(NOTIFICATION_TYPES).toHaveProperty('MEAL_PLAN_READY');
    expect(NOTIFICATION_TYPES).toHaveProperty('MEAL_REPLACEMENT_READY');
    expect(NOTIFICATION_TYPES).toHaveProperty('MEAL_IMAGE_READY');
    expect(NOTIFICATION_TYPES).toHaveProperty('WATER_REMINDER');
    expect(NOTIFICATION_TYPES).toHaveProperty('CUSTOM');
  });
});
