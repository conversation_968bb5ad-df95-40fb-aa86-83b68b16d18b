import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/services/firestore_service.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../../meal_planning/data/models/meal_plan_request.dart';
import '../../../meal_planning/data/services/meal_plan_service.dart';

part 'meal_replacement_service.g.dart';

/// Provider for meal replacement service
@riverpod
Future<MealReplacementService> mealReplacementService(Ref ref) async {
  final localStorageService = await ref.watch(localStorageServiceProvider.future);
  final firestoreService = ref.watch(firestoreServiceProvider);
  final mealPlanService = await ref.watch(mealPlanServiceProvider.future);
  
  return FirebaseMealReplacementService(
    localStorageService,
    firestoreService,
    mealPlanService,
  );
}

/// Result class for meal replacement operations
class MealReplacementOperationResult {
  const MealReplacementOperationResult({
    required this.success,
    this.data,
    this.error,
  });

  final bool success;
  final dynamic data;
  final String? error;
}

/// Abstract meal replacement service interface
abstract class MealReplacementService {
  // Core operations
  Future<MealReplacementOperationResult> generateMealReplacement({
    required GeneratedMeal currentMeal,
    required String dayDocumentId,
    required DateTime mealDate,
    String? customIngredients,
  });

  // State management operations
  Future<void> setReplacementLoadingFlag(String mealId);
  Future<void> clearReplacementLoadingFlag(String mealId);
  bool hasReplacementLoadingFlag(String mealId);

  // Firestore sync operations
  Future<MealReplacementOperationResult> updateMealFromFirestore(String mealId);
  Future<bool> shouldUpdateMealReplacement(String mealId, DateTime completionDateTime);

  // Utility operations
  Future<String?> findDayIdForMeal(String mealId);
  GeneratedMeal? getCurrentMealData(String mealId);
  bool canGenerateMoreReplacements(GeneratedMeal meal);

  // Listener management
  StreamSubscription<DocumentSnapshot>? createReplacementGenerationListener(
    String mealId,
    Function(String mealId, Map<String, dynamic> data) onUpdate,
  );

  // Initialization
  Future<void> initializeListenerIfNeeded(String mealId, Function(String mealId, Map<String, dynamic> data) onUpdate);
}

/// Firebase implementation of meal replacement service
class FirebaseMealReplacementService implements MealReplacementService {
  final LocalStorageService _localStorageService;
  final FirestoreService _firestoreService;
  final MealPlanService _mealPlanService;

  const FirebaseMealReplacementService(
    this._localStorageService,
    this._firestoreService,
    this._mealPlanService,
  );

  @override
  Future<MealReplacementOperationResult> generateMealReplacement({
    required GeneratedMeal currentMeal,
    required String dayDocumentId,
    required DateTime mealDate,
    String? customIngredients,
  }) async {
    try {
      final mealId = currentMeal.id;
      if (mealId == null) {
        return const MealReplacementOperationResult(
          success: false,
          error: 'Meal ID is required for replacement',
        );
      }

      debugPrint('MealReplacementService: Starting replacement generation for meal $mealId');

      // Check if meal can generate more replacements
      if (!canGenerateMoreReplacements(currentMeal)) {
        return const MealReplacementOperationResult(
          success: false,
          error: 'Maximum replacement limit reached (3)',
        );
      }

      // Save loading flag to local storage
      await setReplacementLoadingFlag(mealId);

      // Call Firebase function to generate replacement
      await _mealPlanService.replaceMeal(
        dayDocumentId: dayDocumentId,
        mealId: mealId,
        customIngredients: customIngredients,
      );

      debugPrint('MealReplacementService: Replacement generation started successfully for meal $mealId');
      
      return const MealReplacementOperationResult(
        success: true,
        data: 'Meal replacement generation started successfully',
      );
    } catch (e) {
      debugPrint('MealReplacementService: Error generating meal replacement: $e');
      
      // Clear loading flag on error
      if (currentMeal.id != null) {
        await clearReplacementLoadingFlag(currentMeal.id!);
      }
      
      return MealReplacementOperationResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  @override
  Future<void> setReplacementLoadingFlag(String mealId) async {
    try {
      final loadingData = {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'meal_id': mealId,
      };

      await _localStorageService.setObject('meal_replacement_generation_$mealId', loadingData);
      debugPrint('MealReplacementService: Saved replacement loading flag for meal $mealId');
    } catch (e) {
      debugPrint('MealReplacementService: Error saving replacement loading flag: $e');
    }
  }

  @override
  Future<void> clearReplacementLoadingFlag(String mealId) async {
    try {
      await _localStorageService.remove('meal_replacement_generation_$mealId');
      debugPrint('MealReplacementService: Cleared replacement loading flag for meal $mealId');
    } catch (e) {
      debugPrint('MealReplacementService: Error clearing replacement loading flag: $e');
    }
  }

  @override
  bool hasReplacementLoadingFlag(String mealId) {
    try {
      final loadingData = _localStorageService.getObject('meal_replacement_generation_$mealId');
      final hasFlag = loadingData != null;
      debugPrint('MealReplacementService: Checking replacement loading flag for meal $mealId: $hasFlag');
      return hasFlag;
    } catch (e) {
      debugPrint('MealReplacementService: Error checking replacement loading flag: $e');
      return false;
    }
  }

  @override
  bool canGenerateMoreReplacements(GeneratedMeal meal) {
    const maxReplacements = 3;
    final currentCount = meal.replacementCount;
    final canGenerate = currentCount < maxReplacements;
    
    debugPrint('MealReplacementService: Can generate more replacements for meal ${meal.id}: $canGenerate (current: $currentCount, max: $maxReplacements)');
    return canGenerate;
  }

  @override
  Future<String?> findDayIdForMeal(String mealId) async {
    try {
      debugPrint('MealReplacementService: Finding day ID for meal $mealId');
      
      // Get current meal plan from local storage
      final mealPlanData = _localStorageService.loadCurrentMealPlan();
      if (mealPlanData == null) {
        debugPrint('MealReplacementService: No meal plan found in local storage');
        return null;
      }

      // Parse meal plan data with error handling
      final cleanedMealPlanData = _cleanMealPlanData(mealPlanData);
      final mealPlan = GeneratedMealPlan.fromJson(cleanedMealPlanData);

      // Search through all days to find the meal
      for (final day in mealPlan.days) {
        final meal = day.meals.where((m) => m.id == mealId).firstOrNull;
        if (meal != null) {
          debugPrint('MealReplacementService: Found day ID ${day.dayDocumentId} for meal $mealId');
          return day.dayDocumentId;
        }
      }

      debugPrint('MealReplacementService: No day found for meal $mealId');
      return null;
    } catch (e) {
      debugPrint('MealReplacementService: Error finding day ID for meal: $e');
      return null;
    }
  }

  @override
  GeneratedMeal? getCurrentMealData(String mealId) {
    try {
      debugPrint('MealReplacementService: Getting current meal data for $mealId');
      
      // Get current meal plan from local storage
      final mealPlanData = _localStorageService.loadCurrentMealPlan();
      if (mealPlanData == null) {
        debugPrint('MealReplacementService: No meal plan found in local storage');
        return null;
      }

      // Parse meal plan data with error handling
      final cleanedMealPlanData = _cleanMealPlanData(mealPlanData);
      final mealPlan = GeneratedMealPlan.fromJson(cleanedMealPlanData);

      // Search through all days to find the meal
      for (final day in mealPlan.days) {
        final meal = day.meals.where((m) => m.id == mealId).firstOrNull;
        if (meal != null) {
          debugPrint('MealReplacementService: Found current meal data for $mealId');
          // Return the active meal (applies meal version selection logic)
          return meal.getActiveMeal();
        }
      }

      debugPrint('MealReplacementService: No current meal data found for $mealId');
      return null;
    } catch (e) {
      debugPrint('MealReplacementService: Error getting current meal data: $e');
      return null;
    }
  }

  @override
  Future<MealReplacementOperationResult> updateMealFromFirestore(String mealId) async {
    try {
      debugPrint('MealReplacementService: Updating meal $mealId from Firestore');

      final dayId = await findDayIdForMeal(mealId);
      if (dayId == null) {
        return const MealReplacementOperationResult(
          success: false,
          error: 'Could not find day ID for meal',
        );
      }

      final dayDocument = await _firestoreService.getDocumentFromUserSubcollection(
        subcollectionName: 'days',
        documentId: dayId,
      );

      if (dayDocument == null) {
        return const MealReplacementOperationResult(
          success: false,
          error: 'Day document not found in Firestore',
        );
      }

      await _updateLocalStorageWithDayData(dayDocument);

      return const MealReplacementOperationResult(
        success: true,
        data: 'Meal updated successfully from Firestore',
      );
    } catch (e) {
      debugPrint('MealReplacementService: Error updating meal from Firestore: $e');
      return MealReplacementOperationResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  @override
  Future<bool> shouldUpdateMealReplacement(String mealId, DateTime completionDateTime) async {
    try {
      final currentMeal = getCurrentMealData(mealId);
      if (currentMeal == null) {
        debugPrint('MealReplacementService: No current meal data found, should update');
        return true;
      }

      // Check if the meal has been updated since completion
      final lastUpdated = currentMeal.imageGeneratedAt;
      if (lastUpdated == null) {
        debugPrint('MealReplacementService: No last updated timestamp, should update');
        return true;
      }

      final shouldUpdate = completionDateTime.isAfter(lastUpdated);
      debugPrint('MealReplacementService: Should update meal replacement: $shouldUpdate');
      return shouldUpdate;
    } catch (e) {
      debugPrint('MealReplacementService: Error checking if should update meal replacement: $e');
      return true; // Default to updating on error
    }
  }

  @override
  StreamSubscription<DocumentSnapshot>? createReplacementGenerationListener(
    String mealId,
    Function(String mealId, Map<String, dynamic> data) onUpdate,
  ) {
    try {
      final userId = _firestoreService.currentUserId;
      if (userId == null) {
        debugPrint('MealReplacementService: No user ID available for listener');
        return null;
      }

      debugPrint('MealReplacementService: Creating listener for meal $mealId');

      return FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .collection('bjt_meal_replacement')
          .doc(mealId)
          .snapshots()
          .listen(
            (snapshot) {
              if (snapshot.exists) {
                debugPrint('MealReplacementService: Received update for meal $mealId');
                onUpdate(mealId, snapshot.data()!);
              }
            },
            onError: (error) {
              debugPrint('MealReplacementService: Listener error for meal $mealId: $error');
            },
          );
    } catch (e) {
      debugPrint('MealReplacementService: Error creating listener: $e');
      return null;
    }
  }

  @override
  Future<void> initializeListenerIfNeeded(String mealId, Function(String mealId, Map<String, dynamic> data) onUpdate) async {
    try {
      // Always initialize listener regardless of loading flag
      debugPrint('MealReplacementService: Initializing listener for meal $mealId');
      createReplacementGenerationListener(mealId, onUpdate);
    } catch (e) {
      debugPrint('MealReplacementService: Error initializing listener if needed: $e');
    }
  }

  /// Update local storage with day data from Firestore
  Future<void> _updateLocalStorageWithDayData(Map<String, dynamic> dayDocument) async {
    try {
      // Get current meal plan from local storage
      final mealPlanData = _localStorageService.loadCurrentMealPlan();
      if (mealPlanData == null) {
        debugPrint('MealReplacementService: No meal plan found in local storage for update');
        return;
      }

      // Parse meal plan data with error handling
      final cleanedMealPlanData = _cleanMealPlanData(mealPlanData);
      final mealPlan = GeneratedMealPlan.fromJson(cleanedMealPlanData);

      // Find and update the corresponding day
      final updatedDays = mealPlan.days.map((day) {
        if (day.dayDocumentId == dayDocument['day_document_id']) {
          // Parse the updated day data
          final updatedDay = DayMealPlan.fromJson(dayDocument);
          return updatedDay;
        }
        return day;
      }).toList();

      // Create updated meal plan
      final updatedMealPlan = mealPlan.copyWith(days: updatedDays);

      // Save back to local storage
      await _localStorageService.saveCurrentMealPlan(updatedMealPlan.toJson());
      debugPrint('MealReplacementService: Updated local storage with fresh day data');
    } catch (e) {
      debugPrint('MealReplacementService: Error updating local storage with day data: $e');
    }
  }

  /// Clean meal plan data to handle null boolean values and other compatibility issues
  Map<String, dynamic> _cleanMealPlanData(Map<String, dynamic> mealPlanData) {
    final cleanedData = Map<String, dynamic>.from(mealPlanData);

    // Clean days array
    if (cleanedData['days'] is List) {
      final days = cleanedData['days'] as List;
      cleanedData['days'] = days.map((day) => _cleanDayData(day as Map<String, dynamic>)).toList();
    }

    return cleanedData;
  }

  /// Clean day data to handle null boolean values
  Map<String, dynamic> _cleanDayData(Map<String, dynamic> dayData) {
    final cleanedDay = Map<String, dynamic>.from(dayData);

    // Clean meals array
    if (cleanedDay['meals'] is List) {
      final meals = cleanedDay['meals'] as List;
      cleanedDay['meals'] = meals.map((meal) => _cleanMealData(meal as Map<String, dynamic>)).toList();
    }

    return cleanedDay;
  }

  /// Clean meal data to handle null boolean values
  Map<String, dynamic> _cleanMealData(Map<String, dynamic> mealData) {
    final cleanedMeal = Map<String, dynamic>.from(mealData);

    // Handle boolean fields that might be null
    cleanedMeal['is_consumed'] = cleanedMeal['is_consumed'] ?? false;
    cleanedMeal['isConsumed'] = cleanedMeal['isConsumed'] ?? false;

    // Handle other potential null boolean fields
    if (cleanedMeal['replacement_history'] is List) {
      final replacementHistory = cleanedMeal['replacement_history'] as List;
      cleanedMeal['replacement_history'] = replacementHistory.map((replacement) {
        final cleanedReplacement = Map<String, dynamic>.from(replacement as Map<String, dynamic>);
        cleanedReplacement['is_original'] = cleanedReplacement['is_original'] ?? false;

        // Clean the nested meal data in replacement
        if (cleanedReplacement['meal'] is Map<String, dynamic>) {
          cleanedReplacement['meal'] = _cleanMealData(cleanedReplacement['meal'] as Map<String, dynamic>);
        }

        return cleanedReplacement;
      }).toList();
    }

    return cleanedMeal;
  }
}
