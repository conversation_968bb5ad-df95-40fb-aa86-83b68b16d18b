import 'package:easydietai/features/shopping_list/presentation/pages/shopping_list_details_page.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/utils/logger.dart';
import '../data/models/notification_models.dart';
import '../providers/notification_provider.dart';
import '../../meal_planning/data/models/meal_plan_request.dart';
import '../../meal_details/presentation/pages/meal_details_page.dart';
import '../../meal_planning/providers/current_meal_plan_provider.dart';

/// Service to handle navigation from notification actions
class NotificationNavigationService {
  final Ref _ref;

  NotificationNavigationService(this._ref);

  /// Handle notification action navigation
  Future<void> handleNotificationAction(
    BuildContext context,
    NotificationAction? action,
  ) async {
    if (action == null) {
      AppLogger.warning('NotificationNavigationService: No action provided');
      return;
    }

    switch (action.type) {
      case NotificationActionType.internalNavigation:
        await _handleInternalNavigation(context, action);
        break;
      case NotificationActionType.externalLink:
        await _handleExternalLink(action);
        break;
      case NotificationActionType.none:
      default:
        AppLogger.info('NotificationNavigationService: No action type specified');
        break;
    }
  }

  /// Handle internal app navigation
  Future<void> _handleInternalNavigation(
    BuildContext context,
    NotificationAction action,
  ) async {
    try {
      final route = action.route;
      final parameters = action.parameters ?? {};

      AppLogger.info('NotificationNavigationService: Navigating to $route with parameters: $parameters');

      switch (route) {
        case '/meal-details':
          await _navigateToMealDetails(context, parameters);
          break;
        case '/shopping-list':
          await _navigateToShoppingList(context, parameters);
          break;
        case '/home':
          await _navigateToHome(context, parameters);
          break;
        default:
          // Use go_router for other routes
          if (route != null) {
            context.push(route);
          }
          break;
      }
    } catch (e) {
      AppLogger.error('NotificationNavigationService: Error handling internal navigation: $e');
    }
  }

  /// Navigate to meal details page with specific parameters
  Future<void> _navigateToMealDetails(
    BuildContext context,
    Map<String, dynamic> parameters,
  ) async {
    try {
      final dayDocumentId = parameters['dayDocumentId'] as String?;
      final mealId = parameters['mealId'] as String?;
      final openReplacementModal = parameters['openReplacementModal'] as bool? ?? false;

      if (dayDocumentId == null || mealId == null) {
        AppLogger.error('NotificationNavigationService: Missing required parameters for meal details navigation');
        return;
      }

      // Find the meal and day from current meal plan
      final currentMealPlan = _ref.read(currentMealPlanNotifierProvider);

      if (currentMealPlan.currentPlan?.mealPlan == null) {
        AppLogger.error('NotificationNavigationService: No current meal plan available');
        return;
      }

      // Find the day meal plan by document ID
      DayMealPlan? targetDayMealPlan;
      for (final day in currentMealPlan.currentPlan!.mealPlan.days) {
        if (day.dayDocumentId == dayDocumentId) {
          targetDayMealPlan = day;
          break;
        }
      }

      if (targetDayMealPlan == null) {
        AppLogger.error('NotificationNavigationService: Day meal plan not found for document ID: $dayDocumentId');
        return;
      }

      // Find the meal by ID
      GeneratedMeal? targetMeal;
      for (final meal in targetDayMealPlan.meals) {
        if (meal.id == mealId) {
          targetMeal = meal;
          break;
        }
      }

      if (targetMeal == null) {
        AppLogger.error('NotificationNavigationService: Meal not found for ID: $mealId');
        return;
      }

      // Navigate to meal details page
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => MealDetailsPage(
            meal: targetMeal!,
            dayMealPlan: targetDayMealPlan!,
            date: targetDayMealPlan!.date.toIso8601String(),
            mealType: targetMeal!.type,
            mealSlot: 0, // Default slot, could be extracted from parameters if needed
            openReplacementModal: openReplacementModal,
          ),
        ),
      );

      AppLogger.info('NotificationNavigationService: Successfully navigated to meal details');
    } catch (e) {
      AppLogger.error('NotificationNavigationService: Error navigating to meal details: $e');
    }
  }

  /// Navigate to shopping list page with specific parameters
  Future<void> _navigateToShoppingList(
    BuildContext context,
    Map<String, dynamic> parameters,
  ) async {
    try {
      final shoppingListId = parameters['shoppingListId'] as String?;
      final openNewList = parameters['openNewList'] as bool? ?? false;

      AppLogger.info('NotificationNavigationService: Navigating to shopping list with ID: $shoppingListId');

      // Navigate to shopping list page
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ShoppingListDetailsPage(
            // The shopping list page will load the specific list by ID if provided
            // For now, we'll let it load the latest list and the user can see the new one
          ),
        ),
      );

      AppLogger.info('NotificationNavigationService: Successfully navigated to shopping list');
    } catch (e) {
      AppLogger.error('NotificationNavigationService: Error navigating to shopping list: $e');
    }
  }

  /// Navigate to home page with specific parameters
  Future<void> _navigateToHome(
    BuildContext context,
    Map<String, dynamic> parameters,
  ) async {
    try {
      final focusFirstDay = parameters['focusFirstDay'] as bool? ?? false;
      final firstDayDate = parameters['firstDayDate'] as String?;

      AppLogger.info('NotificationNavigationService: Navigating to home with focusFirstDay: $focusFirstDay, firstDayDate: $firstDayDate');

      // Navigate to home page using go_router
      // The home page should be the root route, so we use go instead of push
      context.go('/');

      // If we need to focus on a specific day, we could store this information
      // in a provider or pass it through the router state
      // For now, the home page will load and show the latest meal plan

      AppLogger.info('NotificationNavigationService: Successfully navigated to home');
    } catch (e) {
      AppLogger.error('NotificationNavigationService: Error navigating to home: $e');
    }
  }

  /// Handle external link navigation
  Future<void> _handleExternalLink(NotificationAction action) async {
    try {
      final url = action.url;
      if (url == null) {
        AppLogger.warning('NotificationNavigationService: No URL provided for external link');
        return;
      }

      // TODO: Implement URL launcher if needed
      AppLogger.info('NotificationNavigationService: External link navigation not implemented: $url');
    } catch (e) {
      AppLogger.error('NotificationNavigationService: Error handling external link: $e');
    }
  }
}

/// Provider for the notification navigation service
final notificationNavigationServiceProvider = Provider<NotificationNavigationService>((ref) {
  return NotificationNavigationService(ref);
});

/// Helper function to handle notification tap from anywhere in the app
Future<void> handleNotificationTap(
  BuildContext context,
  WidgetRef ref,
  AppNotification notification,
) async {
  try {
    // Mark notification as seen asynchronously (don't wait for it to complete)
    ref.read(notificationNotifierProvider.notifier).markNotificationAsSeen(notification.id);

    // Handle navigation action immediately
    final navigationService = ref.read(notificationNavigationServiceProvider);
    await navigationService.handleNotificationAction(context, notification.action);

    AppLogger.info('NotificationNavigationService: Successfully handled notification tap for ${notification.id}');
  } catch (e) {
    AppLogger.error('NotificationNavigationService: Error handling notification tap: $e');
  }
}
