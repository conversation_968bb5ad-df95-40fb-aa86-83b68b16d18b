import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../data/services/meal_days_generation_service.dart';
import 'current_meal_plan_provider.dart';

part 'meal_days_generation_provider.freezed.dart';
part 'meal_days_generation_provider.g.dart';

@freezed
class MealDaysGenerationState with _$MealDaysGenerationState {
  const factory MealDaysGenerationState({
    @Default(false) bool isLoading,
    @Default(false) bool hasError,
    String? errorMessage,
    String? buttonText,
    DateTime? focusedDate,
    @Default(false) bool shouldShowAddDaysCard,
  }) = _MealDaysGenerationState;
}

@riverpod
class MealDaysGenerationNotifier extends _$MealDaysGenerationNotifier {
  StreamSubscription<DocumentSnapshot>? _daysGenerationSubscription;
  MealDaysGenerationService? _service;

  @override
  MealDaysGenerationState build() {
    // Initialize the service asynchronously
    _initializeService();

    // Clean up subscription when provider is disposed
    ref.onDispose(() {
      _daysGenerationSubscription?.cancel();
    });

    return const MealDaysGenerationState();
  }

  /// Initialize the service asynchronously
  Future<void> _initializeService() async {
    try {
      _service = await ref.read(mealDaysGenerationServiceProvider.future);

      // Check local state on initialization
      _checkLocalState();

      // Start listening for days generation updates
      _startDaysGenerationListener();
    } catch (e) {
      debugPrint('MealDaysGenerationNotifier: Error initializing service: $e');
    }
  }

  /// Check local state on initialization
  /// Rule: Check locally for flag of loading if exists then set state to be loading, if not exists then remove state
  void _checkLocalState() {
    try {
      final service = _service;
      if (service == null) return;

      // Check for local loading flag
      final hasLoadingFlag = service.hasLocalLoadingFlag();
      
      if (hasLoadingFlag) {
        // Loading flag exists, set state to loading
        state = state.copyWith(
          isLoading: true,
          hasError: false,
          errorMessage: null,
          buttonText: null,
        );
        debugPrint('MealDaysGenerationNotifier: Found local loading flag, setting state to loading');
      } else {
        // No loading flag, check for error state
        final errorData = service.getLocalErrorData();
        if (errorData != null) {
          final errorMessage = errorData['error_message'] as String? ?? 'حدث خطأ أثناء إنشاء الأيام';
          state = state.copyWith(
            isLoading: false,
            hasError: true,
            errorMessage: errorMessage,
            buttonText: 'إعادة المحاولة',
          );
          debugPrint('MealDaysGenerationNotifier: Found local error, setting error state');
        } else {
          // No loading flag and no error, remove state (set to default)
          state = state.copyWith(
            isLoading: false,
            hasError: false,
            errorMessage: null,
            buttonText: null,
          );
          
          // Set focus for the current date
          _setFocusForCurrentDate();
          debugPrint('MealDaysGenerationNotifier: No local flags, setting default state');
        }
      }
    } catch (e) {
      debugPrint('MealDaysGenerationNotifier: Error checking local state: $e');
    }
  }

  /// Start listening for days generation updates
  /// Rule: Listen to bjt_single/days_generating document
  void _startDaysGenerationListener() {
    try {
      final service = _service;
      if (service == null) return;

      _daysGenerationSubscription = service.createDaysGenerationListener(
        onStatusChange: _handleDaysGenerationStatusChange,
        onError: _handleDaysGenerationError,
      );

      debugPrint('MealDaysGenerationNotifier: Started days generation listener');
    } catch (e) {
      debugPrint('MealDaysGenerationNotifier: Error starting days generation listener: $e');
    }
  }

  /// Handle days generation status changes
  /// Rule: If status is in-progress then set local flag to loading and let state to loading
  /// Rule: If fail then remove flag and set error message and set button text to retry and state is not loading
  /// Rule: If success then check days in local storage and sync if needed
  Future<void> _handleDaysGenerationStatusChange(String status, Map<String, dynamic> data) async {
    try {
      final service = _service;
      if (service == null) return;

      debugPrint('MealDaysGenerationNotifier: Days generation status changed to: $status');

      switch (status) {
        case 'in-progress':
          // Set local flag to loading and update state to loading
          await service.setLocalLoadingFlag();
          state = state.copyWith(
            isLoading: true,
            hasError: false,
            errorMessage: null,
            buttonText: null,
          );
          debugPrint('MealDaysGenerationNotifier: Set loading state for in-progress status');

        case 'fail':
          // Remove flag, set error message, set button text to retry, state is not loading
          await service.clearLocalLoadingFlag();
          final errorMessage = data['status_message'] as String? ?? 'حدث خطأ أثناء إنشاء الأيام';
          await service.saveLocalErrorData(errorMessage);

          state = state.copyWith(
            isLoading: false,
            hasError: true,
            errorMessage: errorMessage,
            buttonText: 'إعادة المحاولة',
          );
          debugPrint('MealDaysGenerationNotifier: Set error state for failed status');

        case 'success':
          // Handle success case with complex logic
          await _handleSuccessfulGeneration(data);

        default:
          debugPrint('MealDaysGenerationNotifier: Unknown status: $status');
      }
    } catch (e) {
      debugPrint('MealDaysGenerationNotifier: Error handling days generation status change: $e');
    }
  }

  /// Handle successful generation
  /// Rule: Check if there are days in local storage, get latest day datetime, generate cutoff date, sync from Firestore
  Future<void> _handleSuccessfulGeneration(Map<String, dynamic> data) async {
    try {
      final service = _service;
      if (service == null) return;

      // Get last completion datetime from the bjt_single data
      final lastCompletionDatetime = data['last_completion_datetime'] as Timestamp?;
      if (lastCompletionDatetime == null) {
        debugPrint('MealDaysGenerationNotifier: No last_completion_datetime in success data');
        return;
      }

      final completionDate = lastCompletionDatetime.toDate();
      debugPrint('MealDaysGenerationNotifier: Last completion datetime: ${completionDate.toIso8601String()}');

      // Check if there are days in local storage
      final latestDayDatetime = service.getLatestDayDatetime();
      
      DateTime cutoffDate;
      if (latestDayDatetime != null) {
        // Days exist, get the latest day datetime
        debugPrint('MealDaysGenerationNotifier: Latest day in local storage: ${latestDayDatetime.toIso8601String()}');
        cutoffDate = latestDayDatetime;
      } else {
        // No days in local storage, generate datetime older than last_completion_datetime by 3 minutes
        cutoffDate = completionDate.subtract(const Duration(minutes: 3));
        debugPrint('MealDaysGenerationNotifier: No local days, using cutoff 3 minutes before completion: ${cutoffDate.toIso8601String()}');
      }

      // Compare the result with last_completion_datetime
      if (completionDate.isBefore(cutoffDate)) {
        // Cutoff is before completion, sync new days from Firestore
        debugPrint('MealDaysGenerationNotifier: Syncing new days from Firestore after: ${cutoffDate.toIso8601String()}');
        
        final syncSuccess = await service.syncNewDaysFromFirestore(cutoffDate);
        if (syncSuccess) {
          debugPrint('MealDaysGenerationNotifier: Successfully synced new days from Firestore');
        } else {
          debugPrint('MealDaysGenerationNotifier: Failed to sync new days from Firestore');
        }
      } else {
        debugPrint('MealDaysGenerationNotifier: No new days to sync (cutoff >= completion)');
      }

      // Clear loading flag and error data
      await service.clearLocalLoadingFlag();
      await service.clearLocalErrorData();

      // Set state loading to false and clear any error
      state = state.copyWith(
        isLoading: false,
        hasError: false,
        errorMessage: null,
        buttonText: null,
      );

      // Set focus for the current date
      _setFocusForCurrentDate();

      // Trigger refresh of current meal plan provider to update UI
      await ref.read(currentMealPlanNotifierProvider.notifier).loadFromLocalStorage();

      debugPrint('MealDaysGenerationNotifier: Successfully handled generation completion');
    } catch (e) {
      debugPrint('MealDaysGenerationNotifier: Error handling successful generation: $e');
    }
  }

  /// Set focus for the current date
  /// Rule: Focus on current date that matches today, or next one larger than current datetime, or show add days card
  void _setFocusForCurrentDate() {
    try {
      final service = _service;
      if (service == null) return;

      final bestDay = service.findBestDayToFocus();
      
      if (bestDay != null) {
        final dateStr = bestDay['date'] as String?;
        if (dateStr != null) {
          final focusDate = DateTime.parse(dateStr);
          state = state.copyWith(
            focusedDate: focusDate,
            shouldShowAddDaysCard: false,
          );
          debugPrint('MealDaysGenerationNotifier: Set focus to date: ${focusDate.toIso8601String()}');
          return;
        }
      }

      // No suitable day found, show add days card
      state = state.copyWith(
        focusedDate: null,
        shouldShowAddDaysCard: true,
      );
      debugPrint('MealDaysGenerationNotifier: No suitable day found, showing add days card');
    } catch (e) {
      debugPrint('MealDaysGenerationNotifier: Error setting focus for current date: $e');
    }
  }

  /// Handle days generation listener errors
  void _handleDaysGenerationError(dynamic error) {
    debugPrint('MealDaysGenerationNotifier: Days generation listener error: $error');
    // Don't change state on listener errors, just log them
  }

  /// Retry generation (called when user taps retry button)
  Future<void> retryGeneration() async {
    try {
      final service = _service;
      if (service == null) return;

      // Clear error state
      await service.clearLocalErrorData();
      
      // Set loading state
      await service.setLocalLoadingFlag();
      state = state.copyWith(
        isLoading: true,
        hasError: false,
        errorMessage: null,
        buttonText: null,
      );

      debugPrint('MealDaysGenerationNotifier: Retry generation initiated');
      
      // Note: The actual generation call should be made by the UI component
      // This provider only manages the state, not the generation itself
    } catch (e) {
      debugPrint('MealDaysGenerationNotifier: Error during retry: $e');
    }
  }

  /// Manual refresh (called by pull-to-refresh or manual sync)
  Future<void> refreshState() async {
    try {
      _checkLocalState();
      debugPrint('MealDaysGenerationNotifier: Manual refresh completed');
    } catch (e) {
      debugPrint('MealDaysGenerationNotifier: Error during manual refresh: $e');
    }
  }
}
