
import 'dart:async';


import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../meal_planning/data/models/meal_plan_request.dart';
import '../widgets/meal_replacement_bottom_sheet.dart';

import '../../../meal_planning/providers/current_meal_plan_provider.dart';
import '../../providers/meal_details_provider.dart';
import '../../providers/meal_replacement_provider.dart';

import '../widgets/meal_details_header.dart';
import '../widgets/meal_image_widget.dart';
import '../widgets/meal_ingredients_list.dart';
import '../widgets/meal_nutrition_info.dart';

import '../widgets/meal_consumption_button.dart';

class MealDetailsPage extends ConsumerStatefulWidget {
  final GeneratedMeal meal;
  final DayMealPlan dayMealPlan;
  final String? date;
  final String? mealType;
  final int? mealSlot;
  final bool openReplacementModal;

  const MealDetailsPage({
    super.key,
    required this.meal,
    required this.dayMealPlan,
    required this.date,
    required this.mealType,
    required this.mealSlot,
    this.openReplacementModal = false,
  });

  @override
  ConsumerState<MealDetailsPage> createState() => _MealDetailsPageState();
}

class _MealDetailsPageState extends ConsumerState<MealDetailsPage> {
  bool _isBottomSheetOpen = false;

  @override
  void initState() {
    super.initState();
    // Initialize meal details provider state and replacement listener if needed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      // Set up callback for automatic bottom sheet opening when replacement completes
      _setupReplacementCompletedCallback();

      // Initialize replacement listener and loading state if meal has loading flag
      _initializeReplacementListenerIfNeeded();

      // Auto-open replacement modal if requested (from notification)
      if (widget.openReplacementModal && mounted && !_isBottomSheetOpen) {
        _showMealSelectionBottomSheet();
      }
    });
  }

  @override
  void dispose() {
    // Note: We cannot use ref.read() in dispose() as the widget is already disposed
    // The providers will be automatically disposed by Riverpod when no longer needed
    super.dispose();
  }



  /// Initialize replacement listener and loading state if meal has loading flag
  void _initializeReplacementListenerIfNeeded() async {
    try {
      if (!mounted) return;

      final mealId = widget.meal.id;
      if (mealId != null) {
        // Initialize loading state and listener
        await ref.read(mealReplacementNotifierProvider.notifier)
            .initializeReplacementLoadingState(mealId);
        await ref.read(mealReplacementNotifierProvider.notifier)
            .initializeListenerIfNeeded(mealId);
      }
    } catch (e) {
      debugPrint('MealDetailsPage: Error initializing replacement listener: $e');
    }
  }

  /// Get the latest replacement datetime from the meal object
  DateTime? _getLatestReplacementDatetime() {
    if (widget.meal.replacementHistory.isEmpty) return null;

    // Find the latest replacement datetime
    DateTime? latestDatetime;
    for (final replacement in widget.meal.replacementHistory) {
      if (latestDatetime == null || replacement.replacedAt.isAfter(latestDatetime)) {
        latestDatetime = replacement.replacedAt;
      }
    }

    return latestDatetime;
  }







  /// Fetch updated meal data from Firestore and update local storage
  Future<void> _fetchAndUpdateMealData() async {
    try {
      final currentMealPlanNotifier = ref.read(currentMealPlanNotifierProvider.notifier);

      // Sync from Firestore to get the latest meal data
      await currentMealPlanNotifier.syncFromFirestore();
    } catch (e) {
      debugPrint('Error fetching and updating meal data: $e');
    }
  }



  /// Get the latest replacement datetime from a specific meal object
  DateTime? _getLatestReplacementDatetimeFromMeal(GeneratedMeal meal) {
    if (meal.replacementHistory.isEmpty) return null;

    // Find the latest replacement datetime
    DateTime? latestDatetime;
    for (final replacement in meal.replacementHistory) {
      if (latestDatetime == null || replacement.replacedAt.isAfter(latestDatetime)) {
        latestDatetime = replacement.replacedAt;
      }
    }

    return latestDatetime;
  }

  /// Get the updated day meal plan from current meal plan state
  DayMealPlan _getUpdatedDayMealPlan(CurrentMealPlanState currentMealPlan) {
    // Try to get the updated day plan from the provider
    final updatedDayPlan = currentMealPlan.currentPlan?.mealPlan.days.firstWhere(
      (DayMealPlan day) => day.date.day == widget.dayMealPlan.date.day &&
               day.date.month == widget.dayMealPlan.date.month &&
               day.date.year == widget.dayMealPlan.date.year,
      orElse: () => widget.dayMealPlan,
    );

    return updatedDayPlan ?? widget.dayMealPlan;
  }

  /// Get the display meal using the meal details provider
  GeneratedMeal _getDisplayMeal(CurrentMealPlanState currentMealPlan) {
    try {
      final updatedDayMealPlan = _getUpdatedDayMealPlan(currentMealPlan);

      return ref.read(mealDetailsNotifierProvider.notifier).getDisplayMeal(
        currentMealPlan,
        widget.meal,
        updatedDayMealPlan,
      );
    } catch (e) {
      debugPrint('MealDetailsPage: Error getting display meal: $e');
      // Return the original meal as fallback
      return widget.meal.getActiveMeal();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch the providers to ensure UI rebuilds when state changes
    final currentMealPlan = ref.watch(currentMealPlanNotifierProvider);
    final displayMeal = _getDisplayMeal(currentMealPlan);
    final updatedDayMealPlan = _getUpdatedDayMealPlan(currentMealPlan);

    // Watch replacement loading state
    final isReplacementLoading = widget.meal.id != null
        ? ref.watch(shouldShowMealReplacementLoadingStateProvider(widget.meal.id!))
        : false;

    // Listen to provider state changes
    ref.listen<CurrentMealPlanState>(currentMealPlanNotifierProvider, (previous, next) {
      if (mounted) {
        try {
          final updatedDayMealPlan = _getUpdatedDayMealPlan(next);

          ref.read(mealDetailsNotifierProvider.notifier).handleCurrentMealStateChange(
            previous,
            next,
            widget.meal,
            updatedDayMealPlan,
          );

          // Meal updates are now handled by the replacement tracker listener
        } catch (e) {
          debugPrint('MealDetailsPage: Error handling meal state change: $e');
        }
      }
    });

    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          // Header
          MealDetailsHeader(
            meal: displayMeal,
            onClose: () => Navigator.of(context).pop(),
            onChangePressed: _handleChangePressed,
            isReplacementLoading: isReplacementLoading,
          ),

          // Scrollable Content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Meal Image (now scrollable)
                  MealImageWidget(
                    meal: displayMeal,
                    dayDocumentId: updatedDayMealPlan.dayDocumentId ?? '',
                    mealDate: updatedDayMealPlan.date,
                  ),

                  // Ingredients List
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                    child: MealIngredientsList(meal: displayMeal),
                  ),
                  const SizedBox(height: 20),

                  // Nutrition Information
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
                    child: MealNutritionInfo(
                      meal: displayMeal,
                      totalDayNutrition: updatedDayMealPlan.totalNutrition,
                    ),
                  ),

                  // Extra padding at bottom to ensure content doesn't get hidden behind fixed sections
                  const SizedBox(height: 100),
                ],
              ),
            ),
          ),

          // Fixed Mark as Taken Button at Bottom
          MealConsumptionButton(
            meal: displayMeal,
            mealDate: updatedDayMealPlan.date,
          ),
        ],
      ),
    );
  }



  /// Handle change button press - show toast if consumed, otherwise show bottom sheet
  void _handleChangePressed() {
    final currentMealPlan = ref.read(currentMealPlanNotifierProvider);
    final displayMeal = _getDisplayMeal(currentMealPlan);

    if (displayMeal.isConsumed) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن تغيير وجبة تم تناولها'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    _showMealSelectionBottomSheet();
  }

  /// Safely update provider state with error handling
  void _safelyUpdateProviderState(void Function() updateFunction) {
    if (!mounted) {
      debugPrint('Widget not mounted, skipping provider state update');
      return;
    }

    try {
      updateFunction();
    } catch (e) {
      debugPrint('Error updating provider state: $e');
      // Don't rethrow to avoid crashing the app
    }
  }

  /// Set up callback for automatic bottom sheet opening when replacement completes
  void _setupReplacementCompletedCallback() {
    try {
      final replacementNotifier = ref.read(mealReplacementNotifierProvider.notifier);
      replacementNotifier.setOnReplacementCompletedCallback((mealId) {
        // Check if the completed replacement is for the current meal and no bottom sheet is open
        if (widget.meal.id == mealId && mounted && !_isBottomSheetOpen) {
          debugPrint('MealDetailsPage: Auto-opening replacement bottom sheet for meal $mealId');
          // Delay slightly to ensure UI state is updated
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted && !_isBottomSheetOpen) {
              _showMealSelectionBottomSheet();
            }
          });
        } else {
          debugPrint('MealDetailsPage: Skipping auto-open - mealId: $mealId, mounted: $mounted, bottomSheetOpen: $_isBottomSheetOpen');
        }
      });
    } catch (e) {
      debugPrint('MealDetailsPage: Error setting up replacement callback: $e');
    }
  }

  /// Show meal selection bottom sheet with replacement options
  void _showMealSelectionBottomSheet() {
    // Check if widget is still mounted and no bottom sheet is already open
    if (!mounted || _isBottomSheetOpen) return;

    try {
      // Get the current data before showing the bottom sheet
      final currentMealPlan = ref.read(currentMealPlanNotifierProvider);
      final updatedDayMealPlan = _getUpdatedDayMealPlan(currentMealPlan);
      final updatedMeal = _getDisplayMeal(currentMealPlan);

      _isBottomSheetOpen = true;

      showModalBottomSheet<void>(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return MealReplacementBottomSheet(
            meal: updatedMeal,
            dayMealPlan: updatedDayMealPlan,
          );
        },
      ).then((_) {
        // Reset flag when bottom sheet is closed
        if (mounted) {
          _isBottomSheetOpen = false;
        }
      });
    } catch (e) {
      debugPrint('Error showing meal selection bottom sheet: $e');
      _isBottomSheetOpen = false;
    }
  }
}