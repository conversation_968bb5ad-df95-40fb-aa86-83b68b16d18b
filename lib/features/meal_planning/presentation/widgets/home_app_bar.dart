import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/router/app_routes.dart';
import '../../../../shared/widgets/notification_badge.dart';
import '../../../../shared/providers/app_state_provider.dart';
import '../../providers/meal_plan_generation_provider.dart';

class HomeAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const HomeAppBar({super.key});

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mealPlanState = ref.watch(mealPlanGenerationNotifierProvider);

    return AppBar(
      title: const Text('خطة الوجبات'),
      leading: NotificationIconButton(
        onPressed: () {
          context.push(AppRoutes.notifications);
        },
      ),
      actions: [
        // Profile Avatar Button
        _buildProfileAvatar(context, ref),
      ],
    );
  }

  /// Build SliverAppBar for use in CustomScrollView with hide/show on scroll behavior
  static Widget buildSliverAppBar(BuildContext context, WidgetRef ref, {Future<void> Function()? onRefresh}) {
    return SliverAppBar(
      title: const Text('خطة الوجبات'),
      floating: true, // Shows immediately when scrolling up
      snap: true, // Snaps to fully visible/hidden state
      leading: NotificationIconButton(
        onPressed: () {
          context.push(AppRoutes.notifications);
        },
      ),
      actions: [
        // Web-only refresh button for pull-to-refresh functionality
        if (kIsWeb && onRefresh != null)
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
            onPressed: onRefresh,
          ),
        // Profile Avatar Button
        _buildProfileAvatar(context, ref),
      ],
      elevation: 0, // Remove default elevation since we're using backdrop filter
      shadowColor: Colors.transparent,
      backgroundColor: Colors.transparent, // Make background transparent for backdrop filter
      foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      flexibleSpace: ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: _getAppBarBackgroundColor(context),
              border: Border(
                bottom: BorderSide(
                  color: Colors.black.withOpacity(0.1),
                  width: 0.5,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build profile avatar with conditional profile picture display
  static Widget _buildProfileAvatar(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(currentUserProvider);
    final userProfile = ref.watch(currentUserProfileProvider);

    // Get profile picture URL from user profile or Firebase user
    final profilePictureUrl = userProfile?.photoUrl ?? currentUser?.photoURL;

    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: GestureDetector(
        onTap: () => context.push('/profile'),
        child: CircleAvatar(
          radius: 18,
          backgroundColor: AppColors.primary.withOpacity(0.1),
          backgroundImage: profilePictureUrl != null && profilePictureUrl.isNotEmpty
              ? _getProfileImageProvider(profilePictureUrl)
              : null,
          onBackgroundImageError: profilePictureUrl != null
              ? (exception, stackTrace) {
                  // Handle image loading error silently
                  // The fallback icon will be shown automatically
                }
              : null,
          child: profilePictureUrl == null || profilePictureUrl.isEmpty
              ? Icon(
                  Icons.person,
                  size: 20,
                  color: AppColors.primary,
                )
              : null,
        ),
      ),
    );
  }

  /// Get appropriate image provider based on the profile picture URL
  static ImageProvider? _getProfileImageProvider(String profilePictureUrl) {
    try {
      // Check if it's a network URL (most common case)
      if (profilePictureUrl.startsWith('http://') ||
          profilePictureUrl.startsWith('https://')) {
        return NetworkImage(profilePictureUrl);
      }

      // Check if it's a local file path
      if (profilePictureUrl.startsWith('/') ||
          profilePictureUrl.contains('\\') ||
          profilePictureUrl.startsWith('file://')) {
        // Remove file:// prefix if present
        final filePath = profilePictureUrl.startsWith('file://')
            ? profilePictureUrl.substring(7)
            : profilePictureUrl;
        return FileImage(File(filePath));
      }

      // Check if it's base64 encoded data
      if (profilePictureUrl.startsWith('data:image/')) {
        // Extract base64 data from data URL
        final base64Data = profilePictureUrl.split(',').last;
        final bytes = base64Decode(base64Data);
        return MemoryImage(bytes);
      } else if (_isBase64String(profilePictureUrl)) {
        // Direct base64 string
        final bytes = base64Decode(profilePictureUrl);
        return MemoryImage(bytes);
      }

      // Default to network image if format is unclear
      return NetworkImage(profilePictureUrl);
    } catch (e) {
      // Return null if there's any error, will fallback to icon
      return null;
    }
  }

  /// Check if a string is a valid base64 string
  static bool _isBase64String(String str) {
    try {
      base64Decode(str);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get appropriate background color for the app bar with frosted glass effect
  static Color _getAppBarBackgroundColor(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    if (brightness == Brightness.dark) {
      return Colors.black.withOpacity(0.8);
    } else {
      return Colors.white.withOpacity(0.8);
    }
  }



}
