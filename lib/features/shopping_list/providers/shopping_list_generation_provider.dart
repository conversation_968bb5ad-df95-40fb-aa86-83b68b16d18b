import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:easydietai/core/services/firestore_service.dart';
import 'package:easydietai/core/services/local_storage_service.dart';
import 'package:easydietai/core/utils/logger.dart';
import 'package:easydietai/features/shopping_list/data/services/shopping_list_sync_service.dart';
import 'package:easydietai/features/shopping_list/providers/shopping_lists_provider.dart';

part 'shopping_list_generation_provider.freezed.dart';
part 'shopping_list_generation_provider.g.dart';

@freezed
class ShoppingListGenerationState with _$ShoppingListGenerationState {
  const factory ShoppingListGenerationState({
    @Default(false) bool isLoading,
    @Default(false) bool hasError,
    String? errorMessage,
    String? buttonText,
    @Default(false) bool shouldShowGenerateCard,
  }) = _ShoppingListGenerationState;
}

@riverpod
class ShoppingListGenerationNotifier extends _$ShoppingListGenerationNotifier {
  StreamSubscription<DocumentSnapshot>? _backgroundJobSubscription;
  LocalStorageService? _localStorageService;
  ShoppingListSyncService? _syncService;

  @override
  ShoppingListGenerationState build() {
    // Initialize services asynchronously
    _initializeServices();

    // Clean up subscription when provider is disposed
    ref.onDispose(() {
      _backgroundJobSubscription?.cancel();
    });

    return const ShoppingListGenerationState();
  }

  /// Initialize services asynchronously
  Future<void> _initializeServices() async {
    try {
      _localStorageService = await ref.read(localStorageServiceProvider.future);
      _syncService = ref.read(shoppingListSyncServiceProvider);

      // Check local state on initialization
      _checkLocalState();

      // Start listening for background job updates
      _startBackgroundJobListener();
    } catch (e) {
      AppLogger.error('ShoppingListGenerationNotifier: Error initializing services: $e');
    }
  }

  /// Check local state on initialization
  /// Rule: Check locally for flag of loading if exists then set state to be loading, if not exists then remove state
  void _checkLocalState() {
    try {
      final syncService = _syncService;
      if (syncService == null) return;

      // Check for local loading flag
      final hasLoadingFlag = syncService.isGenerating();
      
      if (hasLoadingFlag) {
        // Loading flag exists, set state to loading
        state = state.copyWith(
          isLoading: true,
          hasError: false,
          errorMessage: null,
          buttonText: null,
        );
        AppLogger.info('ShoppingListGenerationNotifier: Found local loading flag, setting state to loading');
      } else {
        // No loading flag, check for error state
        final errorData = _getLocalErrorData();
        if (errorData != null) {
          final errorMessage = errorData['error_message'] as String? ?? 'حدث خطأ أثناء إنشاء قائمة التسوق';
          state = state.copyWith(
            isLoading: false,
            hasError: true,
            errorMessage: errorMessage,
            buttonText: 'إعادة المحاولة',
          );
          AppLogger.info('ShoppingListGenerationNotifier: Found local error, setting error state');
        } else {
          // No loading flag and no error, remove state (set to default)
          state = state.copyWith(
            isLoading: false,
            hasError: false,
            errorMessage: null,
            buttonText: null,
          );
          AppLogger.info('ShoppingListGenerationNotifier: No local flags, setting default state');
        }
      }
    } catch (e) {
      AppLogger.error('ShoppingListGenerationNotifier: Error checking local state: $e');
    }
  }

  /// Get local error data from local storage
  Map<String, dynamic>? _getLocalErrorData() {
    try {
      final localStorageService = _localStorageService;
      if (localStorageService == null) return null;

      // Check for shopping list generation error in local storage
      final errorJson = localStorageService.getString('shopping_list_generation_error');
      if (errorJson != null) {
        return {'error_message': errorJson};
      }
      return null;
    } catch (e) {
      AppLogger.error('ShoppingListGenerationNotifier: Error getting local error data: $e');
      return null;
    }
  }

  /// Save local error data to local storage
  Future<void> _saveLocalErrorData(String errorMessage) async {
    try {
      final localStorageService = _localStorageService;
      if (localStorageService == null) return;

      await localStorageService.setString('shopping_list_generation_error', errorMessage);
      AppLogger.info('ShoppingListGenerationNotifier: Saved local error data');
    } catch (e) {
      AppLogger.error('ShoppingListGenerationNotifier: Error saving local error data: $e');
    }
  }

  /// Clear local error data from local storage
  Future<void> _clearLocalErrorData() async {
    try {
      final localStorageService = _localStorageService;
      if (localStorageService == null) return;

      await localStorageService.remove('shopping_list_generation_error');
      AppLogger.info('ShoppingListGenerationNotifier: Cleared local error data');
    } catch (e) {
      AppLogger.error('ShoppingListGenerationNotifier: Error clearing local error data: $e');
    }
  }

  /// Start listening for background job updates
  /// Rule: Listen to bjt_single/bjt_shopping_list document
  void _startBackgroundJobListener() {
    try {
      final firestoreService = ref.read(firestoreServiceProvider);
      final userId = firestoreService.currentUserId;

      if (userId == null) {
        AppLogger.warning('ShoppingListGenerationNotifier: No user available for background job listener');
        return;
      }

      _backgroundJobSubscription = FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .collection('bjt_single')
          .doc('bjt_shopping_list')
          .snapshots()
          .listen(
        _handleBackgroundJobStatusChange,
        onError: _handleBackgroundJobError,
      );

      AppLogger.info('ShoppingListGenerationNotifier: Started background job listener');
    } catch (e) {
      AppLogger.error('ShoppingListGenerationNotifier: Error starting background job listener: $e');
    }
  }

  /// Handle background job status changes
  /// Rule: If status is in-progress then set local flag to loading and let state to loading
  /// Rule: If fail then remove flag and set error message and set button text to retry and state is not loading
  /// Rule: If success then refresh shopping lists and clear flags
  Future<void> _handleBackgroundJobStatusChange(DocumentSnapshot snapshot) async {
    try {
      if (!snapshot.exists) return;

      final data = snapshot.data() as Map<String, dynamic>?;
      if (data == null) return;

      final status = data['status'] as String?;
      if (status == null) return;

      AppLogger.info('ShoppingListGenerationNotifier: Background job status changed to: $status');

      switch (status) {
        case 'in-progress':
          // Set local flag to loading and update state to loading
          final syncService = _syncService;
          if (syncService != null) {
            await syncService.setGeneratingFlag();
          }
          state = state.copyWith(
            isLoading: true,
            hasError: false,
            errorMessage: null,
            buttonText: null,
          );
          AppLogger.info('ShoppingListGenerationNotifier: Set loading state for in-progress status');

        case 'fail':
          // Remove flag, set error message, set button text to retry, state is not loading
          final syncService = _syncService;
          if (syncService != null) {
            await syncService.removeGeneratingFlag();
          }
          final errorMessage = data['status_message'] as String? ?? 'حدث خطأ أثناء إنشاء قائمة التسوق';
          await _saveLocalErrorData(errorMessage);

          state = state.copyWith(
            isLoading: false,
            hasError: true,
            errorMessage: errorMessage,
            buttonText: 'إعادة المحاولة',
          );
          AppLogger.info('ShoppingListGenerationNotifier: Set error state for failed status');

        case 'success':
          // Handle success case
          await _handleSuccessfulGeneration(data);

        default:
          AppLogger.info('ShoppingListGenerationNotifier: Unknown status: $status');
      }
    } catch (e) {
      AppLogger.error('ShoppingListGenerationNotifier: Error handling background job status change: $e');
    }
  }

  /// Handle successful generation
  /// Rule: Clear flags, refresh shopping lists, update state
  Future<void> _handleSuccessfulGeneration(Map<String, dynamic> data) async {
    try {
      // Clear loading flag and error data
      final syncService = _syncService;
      if (syncService != null) {
        await syncService.removeGeneratingFlag();
      }
      await _clearLocalErrorData();

      // Set state loading to false and clear any error
      state = state.copyWith(
        isLoading: false,
        hasError: false,
        errorMessage: null,
        buttonText: null,
      );

      // Trigger refresh of shopping lists provider to update UI
      await ref.read(shoppingListsNotifierProvider.notifier).refreshShoppingLists();

      AppLogger.info('ShoppingListGenerationNotifier: Successfully handled generation completion');
    } catch (e) {
      AppLogger.error('ShoppingListGenerationNotifier: Error handling successful generation: $e');
    }
  }

  /// Handle background job listener errors
  void _handleBackgroundJobError(dynamic error) {
    AppLogger.error('ShoppingListGenerationNotifier: Background job listener error: $error');
    // Don't change state on listener errors, just log them
  }

  /// Retry generation (called when user taps retry button)
  Future<void> retryGeneration() async {
    try {
      // Clear error state
      await _clearLocalErrorData();
      
      // Set loading state
      final syncService = _syncService;
      if (syncService != null) {
        await syncService.setGeneratingFlag();
      }
      state = state.copyWith(
        isLoading: true,
        hasError: false,
        errorMessage: null,
        buttonText: null,
      );

      AppLogger.info('ShoppingListGenerationNotifier: Retry generation initiated');
      
      // Note: The actual generation call should be made by the UI component
      // This provider only manages the state, not the generation itself
    } catch (e) {
      AppLogger.error('ShoppingListGenerationNotifier: Error during retry: $e');
    }
  }

  /// Manual refresh (called by pull-to-refresh or manual sync)
  Future<void> refreshState() async {
    try {
      _checkLocalState();
      AppLogger.info('ShoppingListGenerationNotifier: Manual refresh completed');
    } catch (e) {
      AppLogger.error('ShoppingListGenerationNotifier: Error during manual refresh: $e');
    }
  }
}
