import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../meal_planning/data/models/meal_plan_request.dart';
import '../../../meal_planning/utils/meal_type_colors.dart';
import '../../providers/meal_replacement_provider.dart';

/// Header widget for meal details page displaying meal information with gradient background
class MealDetailsHeader extends ConsumerWidget {
  final GeneratedMeal meal;
  final VoidCallback? onClose;
  final VoidCallback? onChangePressed;
  final bool isReplacementLoading;

  const MealDetailsHeader({
    super.key,
    required this.meal,
    this.onClose,
    this.onChangePressed,
    this.isReplacementLoading = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(16, MediaQuery.of(context).padding.top + 8, 16, 12),
      decoration: BoxDecoration(
        gradient: MealTypeColors.getMealTypeGradient(meal.type),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top row with meal type, consumption status, and close button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Meal type and consumption status
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      _getMealTypeText(meal.type),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  if (meal.isConsumed) ...[
                    const SizedBox(width: 6),
                    Container(
                      padding: const EdgeInsets.all(3),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.check_circle,
                        color: Colors.white,
                        size: 14,
                      ),
                    ),
                  ],
                ],
              ),
              IconButton(
                onPressed: onClose ?? () => Navigator.of(context).pop(),
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 24,
                ),
                padding: const EdgeInsets.all(8),
                constraints: const BoxConstraints(),
              ),
            ],
          ),
          const SizedBox(height: 10),

          // Meal name and change button
          Row(
            children: [
              Expanded(
                child: Text(
                  meal.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              _buildChangeButton(ref),
            ],
          ),
          if (meal.description?.isNotEmpty == true) ...[
            const SizedBox(height: 4),
            Text(
              meal.description!,
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          const SizedBox(height: 10),

          // Quick info row
          Row(
            children: [
              _buildQuickInfo(Icons.restaurant, '${meal.ingredients.length} عنصر'),
              const SizedBox(width: 12),
              _buildQuickInfo(Icons.schedule, '${meal.preparationTime} د'),
              const SizedBox(width: 12),
              _buildQuickInfo(Icons.local_fire_department, '${meal.nutrition.calories} سعرة'),
            ],
          ),
        ],
      ),
    );
  }

  /// Build quick info item with icon and text
  Widget _buildQuickInfo(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          color: Colors.white.withOpacity(0.9),
          size: 14,
        ),
        const SizedBox(width: 3),
        Text(
          text,
          style: TextStyle(
            color: Colors.white.withOpacity(0.9),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Build change button with loading state and error indicator
  Widget _buildChangeButton(WidgetRef ref) {
    final isConsumed = meal.isConsumed;
    final canPress = onChangePressed != null;
    final mealId = meal.id;

    // Check loading and error states
    final isLoading = mealId != null ? ref.watch(shouldShowMealReplacementLoadingProvider(mealId)) : false;
    final hasError = mealId != null ? ref.watch(hasMealReplacementErrorProvider(mealId)) : false;



    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: canPress ? onChangePressed : null,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.4),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isLoading) ...[
                  SizedBox(
                    width: 14,
                    height: 14,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'جاري التحديث...',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ] else ...[
                  Icon(
                    Icons.swap_horiz,
                    color: Colors.white.withOpacity(0.9),
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'تغيير',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  // Show error indicator if there's an error
                  if (hasError) ...[
                    const SizedBox(width: 4),
                    Icon(
                      Icons.error,
                      color: Colors.red.shade300,
                      size: 12,
                    ),
                  ],
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Convert meal type to Arabic text
  String _getMealTypeText(String mealType) {
    switch (mealType.toLowerCase()) {
      case 'breakfast':
        return 'إفطار';
      case 'lunch':
        return 'غداء';
      case 'dinner':
        return 'عشاء';
      case 'snack':
        return 'وجبة خفيفة';
      default:
        return mealType;
    }
  }
}
