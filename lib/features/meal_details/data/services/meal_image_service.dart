import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/firestore_service.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../../meal_planning/data/models/meal_plan_request.dart';
import '../../../meal_planning/data/services/meal_plan_service.dart';

/// Provider for meal image service
final mealImageServiceProvider = FutureProvider<MealImageService>((ref) async {
  final localStorageService = await ref.watch(localStorageServiceProvider.future);
  final firestoreService = ref.watch(firestoreServiceProvider);
  final mealPlanService = await ref.watch(mealPlanServiceProvider.future);

  return FirebaseMealImageService(
    localStorageService,
    firestoreService,
    mealPlanService,
  );
});

/// Result class for meal image operations
class MealImageOperationResult {
  const MealImageOperationResult({
    required this.success,
    this.data,
    this.error,
  });

  final bool success;
  final dynamic data;
  final String? error;
}

/// Abstract meal image service interface
abstract class MealImageService {
  // Core operations
  Future<MealImageOperationResult> generateMealImage({
    String? dayDocumentId,
    String? mealDate,
    required String mealId,
  });

  // State management operations
  Future<void> setGenerationLoadingFlag(String mealId);
  Future<void> clearGenerationLoadingFlag(String mealId);
  bool hasGenerationLoadingFlag(String mealId);

  // Firestore sync operations
  Future<MealImageOperationResult> updateMealFromFirestore(String mealId);
  Future<bool> shouldUpdateMealImage(String mealId, DateTime completionDateTime);

  // Utility operations
  Future<String?> findDayIdForMeal(String mealId);
  GeneratedMeal? getCurrentMealData(String mealId);
  String? convertImageUrlFormat(String originalUrl);
  bool isValidImageUrl(String url);

  // Listener management
  StreamSubscription<DocumentSnapshot>? createImageGenerationListener(
    String mealId,
    Function(String mealId, Map<String, dynamic> data) onUpdate,
  );
}

/// Firebase implementation of meal image service
class FirebaseMealImageService implements MealImageService {
  final LocalStorageService _localStorageService;
  final FirestoreService _firestoreService;
  final MealPlanService _mealPlanService;

  FirebaseMealImageService(
    this._localStorageService,
    this._firestoreService,
    this._mealPlanService,
  );

  @override
  Future<MealImageOperationResult> generateMealImage({
    String? dayDocumentId,
    String? mealDate,
    required String mealId,
  }) async {
    try {
      debugPrint('MealImageService: Starting image generation for meal $mealId');

      // Save loading flag to local storage
      await setGenerationLoadingFlag(mealId);

      // Call Firebase function to generate image
      await _mealPlanService.generateMealImage(
        dayDocumentId: dayDocumentId,
        mealDate: mealDate,
        mealId: mealId,
      );

      debugPrint('MealImageService: Image generation started successfully for meal $mealId');
      
      return const MealImageOperationResult(
        success: true,
        data: 'Image generation started successfully',
      );
    } catch (e) {
      debugPrint('MealImageService: Error generating meal image: $e');
      
      // Clear loading flag on error
      await clearGenerationLoadingFlag(mealId);
      
      return MealImageOperationResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  @override
  Future<void> setGenerationLoadingFlag(String mealId) async {
    try {
      final loadingData = {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'meal_id': mealId,
      };

      await _localStorageService.setObject('meal_image_generation_$mealId', loadingData);
      debugPrint('MealImageService: Saved generation loading flag for meal $mealId');
    } catch (e) {
      debugPrint('MealImageService: Error saving generation loading flag: $e');
    }
  }

  @override
  Future<void> clearGenerationLoadingFlag(String mealId) async {
    try {
      await _localStorageService.remove('meal_image_generation_$mealId');
      debugPrint('MealImageService: Cleared generation loading flag for meal $mealId');
    } catch (e) {
      debugPrint('MealImageService: Error clearing generation loading flag: $e');
    }
  }

  @override
  bool hasGenerationLoadingFlag(String mealId) {
    try {
      final loadingData = _localStorageService.getObject('meal_image_generation_$mealId');
      return loadingData != null;
    } catch (e) {
      debugPrint('MealImageService: Error checking generation loading flag: $e');
      return false;
    }
  }

  @override
  Future<String?> findDayIdForMeal(String mealId) async {
    try {
      final currentPlan = _localStorageService.loadCurrentMealPlan();
      if (currentPlan == null) return null;

      final generatedMealPlan = GeneratedMealPlan.fromJson(currentPlan);

      for (final day in generatedMealPlan.days) {
        for (final meal in day.meals) {
          if (meal.id == mealId) {
            return day.dayDocumentId;
          }
        }
      }

      return null;
    } catch (e) {
      debugPrint('MealImageService: Error finding day ID for meal $mealId: $e');
      return null;
    }
  }

  @override
  GeneratedMeal? getCurrentMealData(String mealId) {
    try {
      final currentPlan = _localStorageService.loadCurrentMealPlan();
      if (currentPlan == null) return null;

      final generatedMealPlan = GeneratedMealPlan.fromJson(currentPlan);

      for (final day in generatedMealPlan.days) {
        for (final meal in day.meals) {
          if (meal.id == mealId) {
            // Return the active meal (applies meal version selection logic)
            return meal.getActiveMeal();
          }
        }
      }

      return null;
    } catch (e) {
      debugPrint('MealImageService: Error getting current meal data: $e');
      return null;
    }
  }

  @override
  String? convertImageUrlFormat(String originalUrl) {
    try {
      debugPrint('MealImageService: Converting URL format for: $originalUrl');

      // If it's already a Firebase Storage download URL, return as is
      if (originalUrl.contains('firebasestorage.googleapis.com') && originalUrl.contains('alt=media')) {
        debugPrint('MealImageService: URL is already in Firebase Storage download format');
        return originalUrl;
      }

      // If it's a regular HTTP/HTTPS URL (like the test URL), return as is
      if (originalUrl.startsWith('http://') || originalUrl.startsWith('https://')) {
        // Only convert storage.googleapis.com URLs
        if (originalUrl.contains('storage.googleapis.com')) {
          final uri = Uri.parse(originalUrl);
          final pathSegments = uri.pathSegments;

          if (pathSegments.length >= 2) {
            final bucket = pathSegments[0];
            final filePath = pathSegments.skip(1).join('/');

            final convertedUrl = 'https://firebasestorage.googleapis.com/v0/b/$bucket/o/${Uri.encodeComponent(filePath)}?alt=media';
            debugPrint('MealImageService: Converted storage.googleapis.com URL to: $convertedUrl');
            return convertedUrl;
          }
        }

        debugPrint('MealImageService: URL is already in HTTP format, returning as is');
        return originalUrl;
      }

      debugPrint('MealImageService: URL format not recognized, returning original');
      return originalUrl;
    } catch (e) {
      debugPrint('MealImageService: Error converting image URL format: $e');
      return originalUrl;
    }
  }

  @override
  bool isValidImageUrl(String url) {
    try {
      // Check if it's a valid URL
      final uri = Uri.tryParse(url);
      if (uri == null || (!uri.hasScheme || (!uri.scheme.startsWith('http')))) {
        debugPrint('MealImageService: Invalid URL format: $url');
        return false;
      }

      // Check if it's a known problematic URL
      if (url.contains('cdn.mos.cms.futurecdn.net')) {
        debugPrint('MealImageService: Known problematic URL detected: $url');
        return false;
      }

      debugPrint('MealImageService: URL validation passed: $url');
      return true;
    } catch (e) {
      debugPrint('MealImageService: Error validating URL: $e');
      return false;
    }
  }

  @override
  Future<bool> shouldUpdateMealImage(String mealId, DateTime completionDateTime) async {
    try {
      final currentPlan = _localStorageService.loadCurrentMealPlan();
      if (currentPlan == null) return false;

      final generatedMealPlan = GeneratedMealPlan.fromJson(currentPlan);

      for (final day in generatedMealPlan.days) {
        for (final meal in day.meals) {
          if (meal.id == mealId) {
            final imageGeneratedAt = meal.imageGeneratedAt;
            if (imageGeneratedAt == null) {
              return true; // No image generated yet, should update
            }
            // Check if completion datetime is at least 5 minutes after image_generated_at
            final fiveMinutesLater = imageGeneratedAt.add(const Duration(minutes: 5));
            return completionDateTime.isAfter(fiveMinutesLater);
          }
        }
      }

      return false; // Meal not found
    } catch (e) {
      debugPrint('MealImageService: Error checking if meal image should be updated: $e');
      return false;
    }
  }

  @override
  Future<MealImageOperationResult> updateMealFromFirestore(String mealId) async {
    try {
      debugPrint('MealImageService: Updating meal $mealId from Firestore');

      final dayId = await findDayIdForMeal(mealId);
      if (dayId == null) {
        return const MealImageOperationResult(
          success: false,
          error: 'Could not find day ID for meal',
        );
      }

      final dayDocument = await _firestoreService.getDocumentFromUserSubcollection(
        subcollectionName: 'days',
        documentId: dayId,
      );

      if (dayDocument == null) {
        return const MealImageOperationResult(
          success: false,
          error: 'Day document not found in Firestore',
        );
      }

      await _updateLocalStorageWithDayData(dayDocument);

      return const MealImageOperationResult(
        success: true,
        data: 'Meal updated successfully from Firestore',
      );
    } catch (e) {
      debugPrint('MealImageService: Error updating meal from Firestore: $e');
      return MealImageOperationResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  @override
  StreamSubscription<DocumentSnapshot>? createImageGenerationListener(
    String mealId,
    Function(String mealId, Map<String, dynamic> data) onUpdate,
  ) {
    final userId = FirebaseAuth.instance.currentUser?.uid;
    if (userId == null) {
      debugPrint('MealImageService: No authenticated user, cannot create listener');
      return null;
    }

    debugPrint('MealImageService: Creating listener for meal $mealId');

    return FirebaseFirestore.instance
        .collection('users')
        .doc(userId)
        .collection('bjt_meal_image_generating')
        .doc(mealId)
        .snapshots()
        .listen(
          (snapshot) {
            if (snapshot.exists) {
              debugPrint('MealImageService: Received update for meal $mealId');
              onUpdate(mealId, snapshot.data()!);
            }
          },
          onError: (error) {
            debugPrint('MealImageService: Listener error for meal $mealId: $error');
          },
        );
  }

  /// Update local storage with updated day data from Firestore
  Future<void> _updateLocalStorageWithDayData(Map<String, dynamic> dayDocument) async {
    try {
      final currentPlan = _localStorageService.loadCurrentMealPlan();
      if (currentPlan == null) {
        debugPrint('MealImageService: No current meal plan in local storage');
        return;
      }

      final generatedMealPlan = GeneratedMealPlan.fromJson(currentPlan);

      // Convert the Firestore day document to DayMealPlan
      final date = (dayDocument['date'] as Timestamp).toDate();
      final mealsData = dayDocument['meals'] as List<dynamic>;
      final nutritionData = dayDocument['total_nutrition'] as Map<String, dynamic>;
      final dayDocumentId = dayDocument['day_document_id'] as String?;

      final meals = mealsData.map((mealData) {
        return GeneratedMeal.fromJson(mealData as Map<String, dynamic>);
      }).toList();

      final totalNutrition = NutritionInfo.fromJson(nutritionData);

      final updatedDay = DayMealPlan(
        date: date,
        meals: meals,
        totalNutrition: totalNutrition,
        dayDocumentId: dayDocumentId,
      );

      // Find and replace the day in the meal plan
      final updatedDays = generatedMealPlan.days.map((day) {
        if (day.dayDocumentId == dayDocumentId) {
          return updatedDay;
        }
        return day;
      }).toList();

      final updatedMealPlan = GeneratedMealPlan(days: updatedDays);
      await _localStorageService.saveCurrentMealPlan(updatedMealPlan.toJson());

      debugPrint('MealImageService: Successfully updated local storage with day data');
    } catch (e) {
      debugPrint('MealImageService: Error updating local storage with day data: $e');
    }
  }
}
