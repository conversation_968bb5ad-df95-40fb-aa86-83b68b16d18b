import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../meal_planning/data/models/meal_plan_request.dart';

/// Provider for meal consumption service
final mealConsumptionServiceProvider = Provider<MealConsumptionService>((ref) {
  return FirebaseMealConsumptionService();
});

/// Result class for meal consumption operations
class MealConsumptionOperationResult {
  const MealConsumptionOperationResult({
    required this.success,
    this.data,
    this.error,
  });

  final bool success;
  final dynamic data;
  final String? error;
}

/// Abstract meal consumption service interface
abstract class MealConsumptionService {
  /// Toggle meal consumption status
  Future<MealConsumptionOperationResult> toggleMealConsumption({
    required DateTime mealDate,
    required String mealType,
  });

  /// Check if meal can be consumed (not a future meal)
  bool canConsumeMeal(DateTime mealDate);

  /// Get consumption button text based on state
  String getConsumptionButtonText(bool isConsumed, bool canToggle);

  /// Get consumption button color based on state
  String getConsumptionButtonColor(bool isConsumed, bool canToggle);
}

/// Firebase implementation of meal consumption service
class FirebaseMealConsumptionService implements MealConsumptionService {
  FirebaseMealConsumptionService();

  @override
  Future<MealConsumptionOperationResult> toggleMealConsumption({
    required DateTime mealDate,
    required String mealType,
  }) async {
    try {
      debugPrint('MealConsumptionService: Toggling consumption for $mealType on ${mealDate.toIso8601String()}');

      // Check if meal can be consumed
      if (!canConsumeMeal(mealDate)) {
        throw Exception('لا يمكن تناول وجبة مستقبلية');
      }

      // Note: The actual consumption toggle will be handled by the provider
      // This service only provides business logic validation
      debugPrint('MealConsumptionService: Consumption validation passed for $mealType');

      return const MealConsumptionOperationResult(success: true);
    } catch (e) {
      debugPrint('MealConsumptionService: Error validating consumption: $e');

      return MealConsumptionOperationResult(
        success: false,
        error: e.toString(),
      );
    }
  }

  @override
  bool canConsumeMeal(DateTime mealDate) {
    final now = DateTime.now();
    final todayDate = DateTime(now.year, now.month, now.day);
    final mealDateOnly = DateTime(mealDate.year, mealDate.month, mealDate.day);
    
    // Can only consume meals from today or past days
    return !mealDateOnly.isAfter(todayDate);
  }

  @override
  String getConsumptionButtonText(bool isConsumed, bool canToggle) {
    if (isConsumed) {
      return 'إلغاء تناول الوجبة';
    } else if (canToggle) {
      return 'تم تناول الوجبة';
    } else {
      return 'لا يمكن تناول وجبة مستقبلية';
    }
  }

  @override
  String getConsumptionButtonColor(bool isConsumed, bool canToggle) {
    if (isConsumed) {
      return 'orange';
    } else if (canToggle) {
      return 'green';
    } else {
      return 'grey';
    }
  }
}
